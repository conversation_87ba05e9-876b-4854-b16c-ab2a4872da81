# AI 抠图功能测试指南

## 功能概述

AI 抠图功能使用 IOPaint 的交互式分割插件，通过点击选择要抠出的物体，生成透明背景的图片。

## 测试步骤

### 1. 启用 AI 抠图模式

- 上传一张包含明显物体的图片
- 点击工具栏中的"AI Cutout"按钮，进入 AI 抠图模式
- 界面应显示绿色的"AI Mode"按钮，表示已激活

### 2. 选择物体

- **左键点击**：选择要保留的物体区域（绿色标记）
- **右键点击**：取消选择的区域（红色标记）
- 每次点击都会在图片上显示相应颜色的圆点标记
- **实时预览**：每次点击后会立即显示 AI 识别的物体轮廓（绿色半透明覆盖）
- 左上角会显示操作说明和清除按钮

### 3. 执行抠图

- 至少点击一次后，"Cut Out"按钮会变为可用状态
- 按钮会显示点击次数，如"Cut Out (3)"
- 点击"Cut Out"按钮执行 AI 抠图
- 处理过程中会显示绿色的加载动画

### 4. 查看结果

- 抠图完成后，选中的物体会被提取，背景变为透明
- 结果图片可以继续用于背景替换或模糊功能

### 5. 背景处理兼容性

- 抠图结果应该能够无缝衔接背景替换功能
- 抠图结果应该能够无缝衔接背景模糊功能

## API 接口测试

### 请求格式

```json
{
  "image": "base64编码的图片",
  "clicks": [[x1, y1, 1], [x2, y2, 0], [x3, y3, 1]],
  "model": "sam2_base"
}
```

### 响应格式

```json
{
  "success": true,
  "maskBase64": "base64编码的mask图片"
}
```

## 实时预览测试

1. **预览响应**：每次点击后应在 1-3 秒内显示预览轮廓
2. **预览准确性**：预览轮廓应准确反映 AI 识别的物体边界
3. **预览清除**：切换模式或清除点击时预览应消失
4. **多次点击**：连续点击应更新预览，显示最新的识别结果

## 错误处理测试

1. **无点击测试**：不点击任何位置直接点击"Cut Out"，应该显示按钮禁用状态
2. **网络错误测试**：断网情况下执行抠图，应该显示错误提示
3. **无效图片测试**：使用损坏的图片，应该显示错误提示
4. **预览失败测试**：预览生成失败时不应影响正常操作

## 用户体验测试

1. **操作提示**：AI 抠图模式下应显示操作说明
2. **视觉反馈**：点击位置应显示颜色标记
3. **状态指示**：按钮状态应正确反映当前操作状态
4. **清除功能**：应能清除所有点击标记

## 性能测试

1. **响应时间**：抠图处理时间应在合理范围内
2. **内存使用**：大图片处理不应导致内存溢出
3. **并发处理**：同时处理多张图片应正常工作
