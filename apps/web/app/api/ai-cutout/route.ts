import { NextRequest, NextResponse } from 'next/server'

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const { image, clicks, model = 'sam2_base' } = await request.json()

    if (!image || !clicks || !Array.isArray(clicks)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required parameters: image and clicks array',
        },
        { status: 400 }
      )
    }

    // Validate clicks format: [[x,y,0/1], [x2,y2,0/1]]
    const validClicks = clicks.every(
      (click) =>
        Array.isArray(click) &&
        click.length === 3 &&
        typeof click[0] === 'number' &&
        typeof click[1] === 'number' &&
        (click[2] === 0 || click[2] === 1)
    )

    if (!validClicks) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid clicks format. Expected: [[x,y,0/1], ...]',
        },
        { status: 400 }
      )
    }

    if (clicks.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'At least one click is required for AI cutout',
        },
        { status: 400 }
      )
    }

    // Get base URL from environment variable or default
    const baseUrl =
      process.env.IOPAINT_BASE_URL ||
      'https://faith1314666-imggen-magic-wand.hf.space'

    // Prepare request body for IOPaint Interactive Segmentation
    const requestBody = {
      name: 'InteractiveSeg',
      image: image,
      clicks: clicks,
      scale: 1.0,
    }

    const response = await fetch(`${baseUrl}/api/v1/run_plugin_gen_mask`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'image/*',
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      const errorText = await response.text()
      return NextResponse.json(
        {
          success: false,
          error: `AI cutout failed: ${response.statusText} - ${errorText}`,
        },
        { status: response.status }
      )
    }

    // Handle different response types
    const contentType = response.headers.get('content-type')

    if (contentType?.includes('image/')) {
      // Direct image response (mask)
      const blob = await response.blob()
      const buffer = await blob.arrayBuffer()
      const base64 = Buffer.from(buffer).toString('base64')

      return NextResponse.json({
        success: true,
        maskBase64: base64,
      })
    } else {
      // JSON response
      const result = await response.text()

      return NextResponse.json({
        success: true,
        maskBase64: result,
      })
    }
  } catch (error) {
    console.error('AI cutout API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
