'use client'

import { useState } from 'react'
import {
  Upload,
  Download,
  ArrowR<PERSON>,
  Clock,
  Zap,
  CheckCircle,
} from 'lucide-react'
import { useTranslations } from 'next-intl'

export function HowToGuide() {
  const t = useTranslations('home')
  const [activeStep, setActiveStep] = useState<number | null>(null)

  const steps = [
    {
      id: 1,
      icon: Upload,
      title: 'Step 1: Upload Your Image',
      description:
        'Simply drag and drop your file or select it from your device. Our tool is designed for high efficiency and supports various formats to help you remove watermark from photo instantly.',
      features: [
        'Drag & drop support',
        'Multiple format support',
        'Instant processing start',
        'Secure upload',
      ],
      color: 'from-blue-500 to-cyan-600',
      bgColor: 'from-blue-50 to-cyan-50',
    },
    {
      id: 2,
      icon: Download,
      title: 'Step 2: Download Your Cleaned Image',
      description:
        "Our ai background remover processes your image in seconds. Your final image is ready for download, with an optional bg blur effect available before you save. It's that simple and fast.",
      features: [
        'Lightning-fast processing',
        'Optional BG blur',
        'High-quality output',
        'Instant download',
      ],
      color: 'from-purple-500 to-pink-600',
      bgColor: 'from-purple-50 to-pink-50',
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('howToGuideTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            {t('howToGuideDescription')}
          </p>
        </div>

        {/* Steps */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {steps.map((step, index) => {
            const IconComponent = step.icon
            const isActive = activeStep === step.id

            return (
              <div
                key={step.id}
                className={`group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 cursor-pointer ${
                  isActive ? 'ring-4 ring-blue-200' : ''
                }`}
                onMouseEnter={() => setActiveStep(step.id)}
                onMouseLeave={() => setActiveStep(null)}
              >
                {/* Step Number */}
                <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                  {step.id}
                </div>

                {/* Icon */}
                <div
                  className={`w-20 h-20 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  <IconComponent className="w-10 h-10 text-white" />
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                    {step.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    {step.description}
                  </p>
                </div>

                {/* Features List */}
                <div
                  className={`mt-6 space-y-3 overflow-hidden transition-all duration-500 ${
                    isActive ? 'max-h-40 opacity-100' : 'max-h-0 opacity-0'
                  }`}
                >
                  {step.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Background Decoration */}
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${step.bgColor} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl pointer-events-none`}
                ></div>
              </div>
            )
          })}
        </div>

        {/* Process Flow Visualization */}
        <div className="relative mb-16">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-8">
              {/* Upload Icon */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <Upload className="w-8 h-8 text-white" />
                </div>
                <span className="text-sm text-gray-600 mt-2 font-medium">
                  Upload
                </span>
              </div>

              {/* Arrow */}
              <div className="flex items-center gap-2">
                <div className="w-8 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400"></div>
                <ArrowRight
                  className="w-6 h-6 text-blue-500 animate-bounce"
                  style={{ animationDirection: 'alternate' }}
                />
                <div className="w-8 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400"></div>
              </div>

              {/* Processing Icon */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
                  <Zap className="w-8 h-8 text-white animate-pulse" />
                </div>
                <span className="text-sm text-gray-600 mt-2 font-medium">
                  Process
                </span>
              </div>

              {/* Arrow */}
              <div className="flex items-center gap-2">
                <div className="w-8 h-0.5 bg-gradient-to-r from-purple-400 to-pink-400"></div>
                <ArrowRight
                  className="w-6 h-6 text-purple-500 animate-bounce"
                  style={{ animationDirection: 'alternate' }}
                />
                <div className="w-8 h-0.5 bg-gradient-to-r from-purple-400 to-pink-400"></div>
              </div>

              {/* Download Icon */}
              <div className="flex flex-col items-center">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <Download className="w-8 h-8 text-white" />
                </div>
                <span className="text-sm text-gray-600 mt-2 font-medium">
                  Download
                </span>
              </div>
            </div>
          </div>

          {/* Time Indicator */}
          <div className="text-center mt-8">
            <div className="inline-flex items-center gap-2 bg-white rounded-full px-6 py-3 shadow-lg border border-gray-100">
              <Clock className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">
                Average processing time: 3-5 seconds
              </span>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <button className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-5 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-4 mx-auto">
            Start Editing Now for Free
            <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
          </button>
          <p className="text-sm text-gray-500 mt-4">
            No account needed • Unlimited usage • Instant results
          </p>
        </div>
      </div>
    </section>
  )
}
