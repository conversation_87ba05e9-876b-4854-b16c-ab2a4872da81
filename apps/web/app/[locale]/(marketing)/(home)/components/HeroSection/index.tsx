'use client'

import { useState, useCallback, useRef } from 'react'
import './hero-animations.css'
import { useRouter } from '@i18n/routing'
import { Upload, ArrowRight, Image as ImageIcon } from 'lucide-react'
import Image from 'next/image'
import { useImageTransfer } from '../../../../../../hooks/useImageTransfer'

const PRESET_BACKGROUNDS = [
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
]

export function HeroSection() {
  const router = useRouter()
  const { urlToTransferData, setImagesToTransfer, filesToTransferData } =
    useImageTransfer()
  const [isDragActive, setIsDragActive] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [loadingPresetIndex, setLoadingPresetIndex] = useState<number | null>(
    null
  )
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Global drag and drop handlers with simplified logic
  const handleGlobalDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Only handle file drags
    if (e.dataTransfer.types.includes('Files')) {
      setIsDragActive(true)
    }
  }, [])

  const handleGlobalDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleGlobalDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Check if we're actually leaving the container
    const relatedTarget = e.relatedTarget as Element
    const currentTarget = e.currentTarget as Element

    // If relatedTarget is null or not contained within currentTarget, we're leaving
    if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
      setIsDragActive(false)
    }
  }, [])

  // Handle file upload (similar to ImageUpload component logic)
  const handleFiles = useCallback(
    async (files: File[]) => {
      if (files.length === 0) return

      // Validate file types
      const validFiles = files.filter(
        (file) =>
          file.type.startsWith('image/') &&
          ['image/jpeg', 'image/png', 'image/webp'].includes(file.type)
      )

      if (validFiles.length === 0) return

      // Validate file sizes (max 10MB each)
      const validSizeFiles = validFiles.filter(
        (file) => file.size <= 10 * 1024 * 1024
      )
      if (validSizeFiles.length === 0) return

      setIsUploading(true)

      try {
        // Convert files to transfer data (similar to ImageUpload progress simulation)
        const transferData = await filesToTransferData(validSizeFiles)

        // 设置shouldShowProgress标志，让ImageEditor显示进度条
        const transferDataWithProgress = transferData.map((data) => ({
          ...data,
          shouldShowProgress: true,
        }))

        setImagesToTransfer(transferDataWithProgress)

        // Navigate to playground
        router.push('/playground')
      } catch (error) {
        console.error('Failed to process files:', error)
      } finally {
        setIsUploading(false)
      }
    },
    [filesToTransferData, setImagesToTransfer, router]
  )

  const handleGlobalDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragActive(false)

      // Only handle file drops
      if (e.dataTransfer.files.length > 0) {
        const files = Array.from(e.dataTransfer.files)
        handleFiles(files)
      }
    },
    [handleFiles]
  )

  const handleUploadClick = () => {
    // Trigger file input click
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files)
      handleFiles(files)
    }
  }

  const handlePresetClick = useCallback(
    async (imageUrl: string, index: number) => {
      if (typeof loadingPresetIndex === 'number') {
        return
      }
      try {
        setLoadingPresetIndex(index)

        // Convert preset image to transfer data
        const transferData = await urlToTransferData(
          imageUrl,
          `preset-${index + 1}.jpg`
        )

        // 设置shouldShowProgress标志，让ImageEditor显示进度条
        const transferDataWithProgress = {
          ...transferData,
          shouldShowProgress: true,
        }

        setImagesToTransfer([transferDataWithProgress])

        // Navigate to playground
        router.push('/playground')
      } catch (error) {
        console.error('Failed to load preset image:', error)
      } finally {
        setTimeout(() => {
          setLoadingPresetIndex(null)
        }, 500)
      }
    },
    [urlToTransferData, setImagesToTransfer, router]
  )

  return (
    <>
      <div
        className="relative min-h-screen flex items-center justify-center px-4 py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50"
        onDragEnter={handleGlobalDragEnter}
        onDragOver={handleGlobalDragOver}
        onDragLeave={handleGlobalDragLeave}
        onDrop={handleGlobalDrop}
      >
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div
            className="absolute top-20 left-10 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse"
            style={{ animationDuration: '4s' }}
          ></div>
          <div
            className="absolute top-40 right-20 w-24 h-24 bg-purple-200 rounded-full opacity-30 animate-bounce"
            style={{ animationDuration: '3s' }}
          ></div>
          <div
            className="absolute bottom-32 left-1/4 w-20 h-20 bg-green-200 rounded-full opacity-25 animate-pulse"
            style={{ animationDuration: '5s' }}
          ></div>
          <div
            className="absolute top-1/2 right-1/3 w-16 h-16 bg-yellow-200 rounded-full opacity-20 animate-ping"
            style={{ animationDuration: '4s' }}
          ></div>
          {/* Floating icons */}
          {/* <div className="absolute opacity-10 top-1/4 left-1/2 w-12 h-12 bg-white rounded-lg shadow-lg opacity-60 animate-float flex items-center justify-center">
            <ImageIcon className="w-6 h-6 text-blue-500" />
          </div> */}
          {/* <div className="absolute bottom-1/4 right-1/4 w-10 h-10 bg-white rounded-full shadow-lg opacity-50 animate-bounce flex items-center justify-center">
            <Upload className="w-5 h-5 text-purple-500" />
          </div> */}
        </div>

        <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-16 items-center relative z-10">
          {/* Left side - Content */}
          <div className="space-y-8 animate-fade-in-left">
            {/* Main heading with animation */}
            <div className="space-y-6">
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight animate-fade-in-up">
                Free & Effortless
                <br />
                <span className="text-blue-600">AI Watermark Remover</span>
                <br />
                and{' '}
                <span className="text-purple-600">AI Background Remover</span>
              </h1>

              <p className="text-lg lg:text-xl text-gray-700 leading-relaxed animate-fade-in-up animate-delay-200">
                Experience the best ai watermark remover available. Quickly
                remove watermark from photo with our simple, efficient, and
                secure tool, ensuring your privacy is always protected.
              </p>
            </div>

            {/* Upload button */}
            <div className="space-y-6 animate-fade-in-up animate-delay-300">
              <button
                onClick={handleUploadClick}
                disabled={isUploading}
                className={`group text-white px-10 py-5 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-4 hover-lift ${
                  isUploading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                }`}
              >
                {isUploading ? (
                  <>
                    <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Upload className="w-6 h-6" />
                    Upload Image
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </>
                )}
              </button>

              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Free & Secure</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>No Registration</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                  <span>Instant Results</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Upload area and presets */}
          <div className="space-y-8 animate-fade-in-right animate-delay-400">
            {/* Upload area */}
            <div
              className="group bg-white/80 backdrop-blur-sm rounded-3xl border-2 border-dashed border-blue-200 p-10 text-center hover:border-purple-300 hover:bg-gradient-to-br hover:from-blue-50/50 hover:to-purple-50/50 transition-all duration-500 min-h-[350px] flex flex-col items-center justify-center cursor-pointer shadow-lg hover:shadow-2xl"
              onClick={handleUploadClick}
            >
              <div className="space-y-6">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 group-hover:from-blue-200 group-hover:to-purple-200 rounded-full flex items-center justify-center mx-auto transition-all duration-500 shadow-lg">
                    <ImageIcon className="w-10 h-10 text-blue-600 group-hover:text-purple-600 transition-colors duration-500" />
                  </div>
                  {/* <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center animate-bounce">
                    <Upload className="w-4 h-4 text-white" />
                  </div> */}
                </div>
                <div>
                  <p className="text-xl font-semibold text-gray-800 group-hover:text-blue-700 mb-3 transition-colors duration-300">
                    Drag & Drop Your Image
                  </p>
                  <p className="text-sm text-gray-600 group-hover:text-purple-600 transition-colors duration-300 leading-relaxed">
                    Support JPG, PNG, WEBP formats
                    <br />
                    Maximum file size: 10MB
                  </p>
                </div>
                <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                  <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
                  <span>AI-Powered Processing</span>
                  <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Preset images */}
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-700 font-semibold">
                  No image? Try these examples:
                </p>
              </div>

              <div className="grid grid-cols-4 gap-4">
                {PRESET_BACKGROUNDS.map((imageUrl, index) => (
                  <button
                    key={index}
                    onClick={() => handlePresetClick(imageUrl, index)}
                    disabled={loadingPresetIndex === index}
                    className={`group relative aspect-square rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-md hover:shadow-xl ${
                      loadingPresetIndex === index
                        ? 'border-blue-500 cursor-not-allowed scale-95'
                        : 'border-gray-200 hover:border-blue-400 transform hover:scale-110'
                    }`}
                  >
                    <Image
                      src={imageUrl}
                      alt={`Example ${index + 1}`}
                      fill
                      className={`object-cover transition-all duration-300 ${
                        loadingPresetIndex === index
                          ? 'opacity-50'
                          : 'group-hover:scale-110'
                      }`}
                      sizes="(max-width: 768px) 25vw, 12vw"
                    />

                    {/* Loading overlay */}
                    {loadingPresetIndex === index ? (
                      <div className="absolute inset-0 bg-blue-500 bg-opacity-30 flex items-center justify-center">
                        <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      </div>
                    ) : (
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                        <div className="bg-white/90 rounded-full p-2 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                          <ArrowRight className="w-4 h-4 text-blue-600" />
                        </div>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Privacy notice */}
            <div className="text-xs text-gray-500 text-center space-y-1 bg-gray-50/50 rounded-lg p-3">
              <p className="font-medium">🔒 Your privacy is protected</p>
              <p>
                Images are processed securely and automatically deleted after 24
                hours.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Drag overlay */}
      {isDragActive && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center p-4 pointer-events-none">
          <div className="bg-white/95 backdrop-blur-md rounded-3xl border-4 border-dashed border-blue-400 p-12 max-w-2xl w-full text-center shadow-2xl relative">
            {/* Corner decorations */}
            <div className="absolute top-4 left-4 w-8 h-8 border-l-4 border-t-4 border-blue-400 rounded-tl-lg"></div>
            <div className="absolute top-4 right-4 w-8 h-8 border-r-4 border-t-4 border-blue-400 rounded-tr-lg"></div>
            <div className="absolute bottom-4 left-4 w-8 h-8 border-l-4 border-b-4 border-blue-400 rounded-bl-lg"></div>
            <div className="absolute bottom-4 right-4 w-8 h-8 border-r-4 border-b-4 border-blue-400 rounded-br-lg"></div>

            {/* Upload icon */}
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Upload className="w-10 h-10 text-blue-500" />
            </div>

            <div className="text-4xl font-bold text-gray-800 mb-4">
              Drop image here
            </div>
            <div className="text-lg text-gray-600 mb-8">
              Release to upload and start editing
            </div>

            {/* File type indicators */}
            <div className="flex justify-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg border border-blue-200">
                <div className="w-8 h-8 bg-blue-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">JPG</div>
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-lg border border-green-200">
                <div className="w-8 h-8 bg-green-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">PNG</div>
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg border border-purple-200">
                <div className="w-8 h-8 bg-purple-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">WEBP</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        multiple
        onChange={handleFileInputChange}
        className="hidden"
      />
    </>
  )
}
