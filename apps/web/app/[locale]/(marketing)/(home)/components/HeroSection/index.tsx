'use client'

import { useState, useCallback, useRef } from 'react'
import './hero-animations.css'
import { useRouter } from '@i18n/routing'
import { Upload, ArrowRight, Image as ImageIcon } from 'lucide-react'
import Image from 'next/image'
import { useImageTransfer } from '../../../../../../hooks/useImageTransfer'

const PRESET_BACKGROUNDS = [
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&w=600&loading=lazy',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
  'https://images.pexels.com/photos/********/pexels-photo-********.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
]

export function HeroSection() {
  const router = useRouter()
  const { urlToTransferData, setImagesToTransfer, filesToTransferData } =
    useImageTransfer()
  const [isDragActive, setIsDragActive] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [loadingPresetIndex, setLoadingPresetIndex] = useState<number | null>(
    null
  )
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Global drag and drop handlers with simplified logic
  const handleGlobalDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Only handle file drags
    if (e.dataTransfer.types.includes('Files')) {
      setIsDragActive(true)
    }
  }, [])

  const handleGlobalDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleGlobalDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Check if we're actually leaving the container
    const relatedTarget = e.relatedTarget as Element
    const currentTarget = e.currentTarget as Element

    // If relatedTarget is null or not contained within currentTarget, we're leaving
    if (!relatedTarget || !currentTarget.contains(relatedTarget)) {
      setIsDragActive(false)
    }
  }, [])

  // Handle file upload (similar to ImageUpload component logic)
  const handleFiles = useCallback(
    async (files: File[]) => {
      if (files.length === 0) return

      // Validate file types
      const validFiles = files.filter(
        (file) =>
          file.type.startsWith('image/') &&
          ['image/jpeg', 'image/png', 'image/webp'].includes(file.type)
      )

      if (validFiles.length === 0) return

      // Validate file sizes (max 10MB each)
      const validSizeFiles = validFiles.filter(
        (file) => file.size <= 10 * 1024 * 1024
      )
      if (validSizeFiles.length === 0) return

      setIsUploading(true)

      try {
        // Convert files to transfer data (similar to ImageUpload progress simulation)
        const transferData = await filesToTransferData(validSizeFiles)

        // 设置shouldShowProgress标志，让ImageEditor显示进度条
        const transferDataWithProgress = transferData.map((data) => ({
          ...data,
          shouldShowProgress: true,
        }))

        setImagesToTransfer(transferDataWithProgress)

        // Navigate to playground
        router.push('/playground')
      } catch (error) {
        console.error('Failed to process files:', error)
      } finally {
        setIsUploading(false)
      }
    },
    [filesToTransferData, setImagesToTransfer, router]
  )

  const handleGlobalDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragActive(false)

      // Only handle file drops
      if (e.dataTransfer.files.length > 0) {
        const files = Array.from(e.dataTransfer.files)
        handleFiles(files)
      }
    },
    [handleFiles]
  )

  const handleUploadClick = () => {
    // Trigger file input click
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files)
      handleFiles(files)
    }
  }

  const handlePresetClick = useCallback(
    async (imageUrl: string, index: number) => {
      if (typeof loadingPresetIndex === 'number') {
        return
      }
      try {
        setLoadingPresetIndex(index)

        // Convert preset image to transfer data
        const transferData = await urlToTransferData(
          imageUrl,
          `preset-${index + 1}.jpg`
        )

        // 设置shouldShowProgress标志，让ImageEditor显示进度条
        const transferDataWithProgress = {
          ...transferData,
          shouldShowProgress: true,
        }

        setImagesToTransfer([transferDataWithProgress])

        // Navigate to playground
        router.push('/playground')
      } catch (error) {
        console.error('Failed to load preset image:', error)
      } finally {
        setTimeout(() => {
          setLoadingPresetIndex(null)
        }, 500)
      }
    },
    [urlToTransferData, setImagesToTransfer, router]
  )

  return (
    <>
      <div
        className="relative min-h-screen flex items-center justify-center px-4 py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50"
        onDragEnter={handleGlobalDragEnter}
        onDragOver={handleGlobalDragOver}
        onDragLeave={handleGlobalDragLeave}
        onDrop={handleGlobalDrop}
      >
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Subtle geometric shapes */}
          <div
            className="absolute top-20 left-10 w-24 h-24 bg-blue-100 rounded-full opacity-30 animate-pulse"
            style={{ animationDuration: '6s' }}
          ></div>
          <div
            className="absolute top-32 right-16 w-16 h-16 bg-purple-100 rounded-full opacity-25 animate-bounce"
            style={{ animationDuration: '4s' }}
          ></div>
          <div
            className="absolute bottom-40 left-1/5 w-20 h-20 bg-green-100 rounded-full opacity-20 animate-pulse"
            style={{ animationDuration: '8s' }}
          ></div>
          <div
            className="absolute top-1/3 right-1/4 w-12 h-12 bg-yellow-100 rounded-full opacity-15 animate-ping"
            style={{ animationDuration: '5s' }}
          ></div>

          {/* Floating tech icons - positioned better */}
          <div className="absolute top-1/4 left-1/6 w-10 h-10 bg-white/80 backdrop-blur-sm rounded-lg shadow-md opacity-70 animate-float flex items-center justify-center">
            <ImageIcon className="w-5 h-5 text-blue-500" />
          </div>
          <div className="absolute bottom-1/3 right-1/6 w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full shadow-md opacity-60 animate-bounce flex items-center justify-center">
            <Upload className="w-4 h-4 text-purple-500" />
          </div>
          <div className="absolute top-2/3 left-1/12 w-6 h-6 bg-white/70 backdrop-blur-sm rounded-full shadow-sm opacity-50 animate-pulse flex items-center justify-center">
            <ArrowRight className="w-3 h-3 text-green-500" />
          </div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          {/* Centered Content Layout */}
          <div className="text-center mb-16 animate-fade-in-up">
            {/* Main heading */}
            <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight mb-8">
              Free & Effortless
              <br />
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
                AI Watermark Remover
              </span>
              <br />
              <span className="text-gray-700">and</span>{' '}
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent">
                AI Background Remover
              </span>
            </h1>

            <p className="text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto mb-12 animate-fade-in-up animate-delay-200">
              Experience the best ai watermark remover available. Quickly remove
              watermark from photo with our simple, efficient, and secure tool,
              ensuring your privacy is always protected.
            </p>

            {/* Feature highlights */}
            <div className="flex flex-wrap items-center justify-center gap-6 mb-12 animate-fade-in-up animate-delay-300">
              <div className="flex items-center gap-2 bg-green-50 text-green-700 px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="font-medium">100% Free</span>
              </div>
              <div className="flex items-center gap-2 bg-blue-50 text-blue-700 px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="font-medium">No Registration</span>
              </div>
              <div className="flex items-center gap-2 bg-purple-50 text-purple-700 px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                <span className="font-medium">Instant Results</span>
              </div>
              <div className="flex items-center gap-2 bg-orange-50 text-orange-700 px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                <span className="font-medium">Privacy Protected</span>
              </div>
            </div>
          </div>

          {/* Upload Section - Centered */}
          <div className="max-w-4xl mx-auto animate-fade-in-up animate-delay-400">
            {/* Main Upload Area */}
            <div
              className="group bg-white/90 backdrop-blur-sm rounded-3xl border-2 border-dashed border-blue-200 p-12 text-center hover:border-purple-300 hover:bg-gradient-to-br hover:from-blue-50/30 hover:to-purple-50/30 transition-all duration-500 min-h-[400px] flex flex-col items-center justify-center cursor-pointer shadow-xl hover:shadow-2xl"
              onClick={handleUploadClick}
            >
              <div className="space-y-8">
                {/* Upload Icon with Animation */}
                <div className="relative">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 group-hover:from-blue-200 group-hover:to-purple-200 rounded-full flex items-center justify-center mx-auto transition-all duration-500 shadow-lg group-hover:scale-110">
                    <ImageIcon className="w-12 h-12 text-blue-600 group-hover:text-purple-600 transition-colors duration-500" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-10 h-10 bg-green-500 rounded-full flex items-center justify-center animate-bounce shadow-lg">
                    <Upload className="w-5 h-5 text-white" />
                  </div>
                </div>

                {/* Upload Text */}
                <div className="space-y-4">
                  <button
                    onClick={handleUploadClick}
                    disabled={isUploading}
                    className={`group text-white px-12 py-4 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-4 mx-auto hover-lift ${
                      isUploading
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                    }`}
                  >
                    {isUploading ? (
                      <>
                        <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Upload className="w-6 h-6" />
                        Upload Image
                        <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                      </>
                    )}
                  </button>

                  <p className="text-lg text-gray-700 group-hover:text-blue-700 font-medium transition-colors duration-300">
                    or drag & drop your image here
                  </p>
                  <p className="text-sm text-gray-500 group-hover:text-purple-600 transition-colors duration-300 leading-relaxed">
                    Support JPG, PNG, WEBP formats • Maximum file size: 10MB
                  </p>
                </div>

                {/* Processing Indicator */}
                <div className="flex items-center justify-center gap-3 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                    <span>AI-Powered</span>
                  </div>
                  <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                  <div className="flex items-center gap-1">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
                    <span>Secure Processing</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Preset images */}
            <div className="mt-12 space-y-6">
              <div className="text-center">
                <p className="text-lg text-gray-700 font-semibold mb-2">
                  No image? Try these examples:
                </p>
                <p className="text-sm text-gray-500">
                  Click any image below to see our AI in action
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {PRESET_BACKGROUNDS.map((imageUrl, index) => (
                  <button
                    key={index}
                    onClick={() => handlePresetClick(imageUrl, index)}
                    disabled={loadingPresetIndex === index}
                    className={`group relative aspect-square rounded-2xl overflow-hidden border-2 transition-all duration-300 shadow-lg hover:shadow-2xl ${
                      loadingPresetIndex === index
                        ? 'border-blue-500 cursor-not-allowed scale-95'
                        : 'border-gray-200 hover:border-blue-400 transform hover:scale-105'
                    }`}
                  >
                    <Image
                      src={imageUrl}
                      alt={`Example ${index + 1}`}
                      fill
                      className={`object-cover transition-all duration-300 ${
                        loadingPresetIndex === index
                          ? 'opacity-50'
                          : 'group-hover:scale-110'
                      }`}
                      sizes="(max-width: 768px) 50vw, 25vw"
                    />

                    {/* Loading overlay */}
                    {loadingPresetIndex === index ? (
                      <div className="absolute inset-0 bg-blue-500 bg-opacity-30 flex items-center justify-center">
                        <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      </div>
                    ) : (
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                        <div className="bg-white/95 rounded-full p-3 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300 shadow-lg">
                          <ArrowRight className="w-5 h-5 text-blue-600" />
                        </div>
                      </div>
                    )}

                    {/* Example label */}
                    <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                      Example {index + 1}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Privacy notice */}
            <div className="mt-8 text-center">
              <div className="inline-flex items-center gap-2 bg-green-50 text-green-700 px-6 py-3 rounded-full text-sm font-medium">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>
                  🔒 Your privacy is protected - Images deleted after 24 hours
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Drag overlay */}
      {isDragActive && (
        <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center p-4 pointer-events-none">
          <div className="bg-white/95 backdrop-blur-md rounded-3xl border-4 border-dashed border-blue-400 p-12 max-w-2xl w-full text-center shadow-2xl relative">
            {/* Corner decorations */}
            <div className="absolute top-4 left-4 w-8 h-8 border-l-4 border-t-4 border-blue-400 rounded-tl-lg"></div>
            <div className="absolute top-4 right-4 w-8 h-8 border-r-4 border-t-4 border-blue-400 rounded-tr-lg"></div>
            <div className="absolute bottom-4 left-4 w-8 h-8 border-l-4 border-b-4 border-blue-400 rounded-bl-lg"></div>
            <div className="absolute bottom-4 right-4 w-8 h-8 border-r-4 border-b-4 border-blue-400 rounded-br-lg"></div>

            {/* Upload icon */}
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Upload className="w-10 h-10 text-blue-500" />
            </div>

            <div className="text-4xl font-bold text-gray-800 mb-4">
              Drop image here
            </div>
            <div className="text-lg text-gray-600 mb-8">
              Release to upload and start editing
            </div>

            {/* File type indicators */}
            <div className="flex justify-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg border border-blue-200">
                <div className="w-8 h-8 bg-blue-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">JPG</div>
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-lg border border-green-200">
                <div className="w-8 h-8 bg-green-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">PNG</div>
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg border border-purple-200">
                <div className="w-8 h-8 bg-purple-500 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold">WEBP</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        multiple
        onChange={handleFileInputChange}
        className="hidden"
      />
    </>
  )
}
