'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Play, Pause, RotateCcw, Brush } from 'lucide-react'

interface ObjectRemovalDemoProps {
  beforeImage?: string
  afterImage?: string
  className?: string
}

export function ObjectRemovalDemo({
  beforeImage = 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=600&h=400&fit=crop',
  afterImage = 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=600&h=400&fit=crop',
  className = '',
}: ObjectRemovalDemoProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [phase, setPhase] = useState<
    'idle' | 'brushing' | 'processing' | 'complete'
  >('idle')
  const [brushStrokes, setBrushStrokes] = useState<
    Array<{ x: number; y: number; size: number }>
  >([])
  const [isHovered, setIsHovered] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>()

  // Predefined brush stroke path (simulating user drawing)
  const strokePath = [
    { x: 0.3, y: 0.4, size: 20 },
    { x: 0.32, y: 0.42, size: 22 },
    { x: 0.35, y: 0.45, size: 25 },
    { x: 0.38, y: 0.47, size: 23 },
    { x: 0.42, y: 0.48, size: 21 },
    { x: 0.45, y: 0.46, size: 24 },
    { x: 0.48, y: 0.44, size: 26 },
    { x: 0.5, y: 0.42, size: 22 },
    { x: 0.52, y: 0.4, size: 20 },
  ]

  const drawBrushStroke = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      stroke: { x: number; y: number; size: number },
      canvasWidth: number,
      canvasHeight: number
    ) => {
      ctx.globalCompositeOperation = 'source-over'
      ctx.fillStyle = 'rgba(255, 0, 0, 0.6)'
      ctx.beginPath()
      ctx.arc(
        stroke.x * canvasWidth,
        stroke.y * canvasHeight,
        stroke.size,
        0,
        2 * Math.PI
      )
      ctx.fill()
    },
    []
  )

  const animateBrushing = useCallback(() => {
    if (!canvasRef.current || !containerRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    const rect = containerRef.current.getBoundingClientRect()
    canvas.width = rect.width
    canvas.height = rect.height

    let strokeIndex = 0
    const brushingDuration = 2000 // 2 seconds
    const strokeInterval = brushingDuration / strokePath.length

    const drawNextStroke = () => {
      if (strokeIndex < strokePath.length && phase === 'brushing') {
        const newStroke = strokePath[strokeIndex]
        setBrushStrokes((prev) => [...prev, newStroke])

        // Clear and redraw all strokes
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        brushStrokes.concat([newStroke]).forEach((stroke) => {
          drawBrushStroke(ctx, stroke, canvas.width, canvas.height)
        })

        strokeIndex++
        setTimeout(drawNextStroke, strokeInterval)
      } else if (strokeIndex >= strokePath.length) {
        // Start processing phase
        setPhase('processing')
      }
    }

    drawNextStroke()
  }, [phase, brushStrokes, drawBrushStroke])

  const animateProcessing = useCallback(() => {
    if (!canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    let flashCount = 0
    const maxFlashes = 6
    const flashInterval = 200

    const flash = () => {
      if (flashCount < maxFlashes && phase === 'processing') {
        // Alternate between showing and hiding the brush strokes
        if (flashCount % 2 === 0) {
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          // Add white flash effect
          ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
        } else {
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          // Redraw brush strokes with fading effect
          brushStrokes.forEach((stroke) => {
            ctx.globalCompositeOperation = 'source-over'
            ctx.fillStyle = `rgba(255, 0, 0, ${0.6 - flashCount * 0.1})`
            ctx.beginPath()
            ctx.arc(
              stroke.x * canvas.width,
              stroke.y * canvas.height,
              stroke.size,
              0,
              2 * Math.PI
            )
            ctx.fill()
          })
        }

        flashCount++
        setTimeout(flash, flashInterval)
      } else {
        // Complete the animation
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        setPhase('complete')
        setTimeout(() => {
          restart()
        }, 2000)
      }
    }

    flash()
  }, [phase, brushStrokes])

  const play = () => {
    if (phase === 'idle') {
      setIsPlaying(true)
      setPhase('brushing')
      setBrushStrokes([])
    }
  }

  const pause = () => {
    setIsPlaying(false)
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
    }
  }

  const restart = () => {
    setPhase('idle')
    setIsPlaying(false)
    setBrushStrokes([])
    if (canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height)
      }
    }
    // Auto restart after a brief pause
    setTimeout(() => {
      play()
    }, 1000)
  }

  useEffect(() => {
    if (phase === 'brushing') {
      animateBrushing()
    } else if (phase === 'processing') {
      animateProcessing()
    }
  }, [phase, animateBrushing, animateProcessing])

  useEffect(() => {
    // Auto start animation when component mounts
    const timer = setTimeout(() => {
      play()
    }, 500)

    return () => {
      clearTimeout(timer)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  return (
    <div
      className={`relative group ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Container */}
      <div
        ref={containerRef}
        className="relative w-full h-80 rounded-2xl overflow-hidden shadow-2xl bg-gray-100"
      >
        {/* Before Image (always visible) */}
        <div className="absolute inset-0">
          <img
            src={beforeImage}
            alt="Before - with unwanted objects"
            className="w-full h-full object-cover"
          />
        </div>

        {/* After Image (shown when complete) */}
        {phase === 'complete' && (
          <div className="absolute inset-0 animate-fade-in">
            <img
              src={afterImage}
              alt="After - objects removed"
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold animate-bounce">
              ✨ Cleaned!
            </div>
          </div>
        )}

        {/* Canvas for brush strokes */}
        <canvas
          ref={canvasRef}
          className="absolute inset-0 pointer-events-none z-10"
          style={{
            mixBlendMode: phase === 'processing' ? 'multiply' : 'normal',
          }}
        />

        {/* Phase indicators */}
        <div className="absolute top-4 left-4">
          {phase === 'idle' && (
            <div className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
              Ready
            </div>
          )}
          {phase === 'brushing' && (
            <div className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-2">
              <Brush className="w-4 h-4" />
              Marking Objects
            </div>
          )}
          {phase === 'processing' && (
            <div className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
              AI Processing...
            </div>
          )}
        </div>

        {/* Control buttons */}
        <div
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-opacity duration-300 ${
            isHovered && phase === 'idle' ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <div className="flex items-center gap-2 bg-black/70 backdrop-blur-sm rounded-full p-2">
            <button
              onClick={play}
              disabled={phase !== 'idle'}
              className="w-12 h-12 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors duration-200 disabled:opacity-50"
            >
              <Play className="w-6 h-6 text-gray-700 ml-0.5" />
            </button>
            <button
              onClick={restart}
              className="w-12 h-12 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors duration-200"
            >
              <RotateCcw className="w-6 h-6 text-gray-700" />
            </button>
          </div>
        </div>

        {/* Progress indicator */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-black/50 backdrop-blur-sm rounded-full p-2">
            <div className="flex items-center gap-2 text-white text-xs">
              <div
                className={`w-2 h-2 rounded-full ${
                  phase === 'brushing'
                    ? 'bg-orange-400 animate-pulse'
                    : phase === 'processing'
                    ? 'bg-purple-400 animate-pulse'
                    : phase === 'complete'
                    ? 'bg-green-400'
                    : 'bg-gray-400'
                }`}
              ></div>
              <span>
                {phase === 'idle' && 'Click to start demo'}
                {phase === 'brushing' && 'Marking unwanted objects...'}
                {phase === 'processing' && 'AI removing objects...'}
                {phase === 'complete' && 'Objects successfully removed!'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="mt-4 text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Smart Object Removal
        </h3>
        <p className="text-sm text-gray-600">
          Simply mark unwanted objects and watch our AI intelligently remove
          them
        </p>
      </div>
    </div>
  )
}
