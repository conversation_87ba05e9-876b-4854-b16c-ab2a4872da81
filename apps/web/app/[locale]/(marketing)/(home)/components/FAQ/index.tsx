'use client'

import { useState } from 'react'
import { ChevronDown, Shield, Zap, Star, Clock } from 'lucide-react'

export function FAQ() {
  const [openFAQ, setOpenFAQ] = useState<number | null>(1)

  const faqs = [
    {
      id: 1,
      icon: Shield,
      question:
        'How does the free watermark remover work while ensuring privacy?',
      answer:
        'Our ai watermark remover processes images on secure, encrypted servers and automatically deletes all data after 24 hours. We prioritize user privacy, so you can edit with peace of mind.',
      color: 'from-blue-500 to-cyan-600',
    },
    {
      id: 2,
      icon: Zap,
      question: 'Can I really remove text from an image with this tool?',
      answer:
        'Yes, our advanced ai background remover algorithm can identify and reconstruct the area behind text overlays, helping you clean up image files for any use case without leaving artifacts.',
      color: 'from-purple-500 to-pink-600',
    },
    {
      id: 3,
      icon: Star,
      question: 'Is your ai watermark remover better than other online tools?',
      answer:
        'Our tool offers a unique combination of speed, accuracy, and free features. You can even add a professional bg blur, making it a versatile all-in-one editor for all your needs.',
      color: 'from-green-500 to-emerald-600',
    },
    {
      id: 4,
      icon: Clock,
      question: 'What is the process to remove a watermark from a photo?',
      answer:
        'Simply upload your image, and our ai watermark remover automatically detects and erases the watermark. The process is fully automated for maximum efficiency, giving you a clean image in seconds.',
      color: 'from-orange-500 to-red-600',
    },
  ]

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id)
  }

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-4xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-gray-700 leading-relaxed">
            Everything you need to know about our AI-powered image editing tools
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-6">
          {faqs.map((faq) => {
            const IconComponent = faq.icon
            const isOpen = openFAQ === faq.id

            return (
              <div
                key={faq.id}
                className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full p-6 text-left flex items-center gap-4 hover:bg-gray-50 transition-colors duration-200"
                >
                  {/* Icon */}
                  <div
                    className={`flex-shrink-0 w-12 h-12 bg-gradient-to-r ${faq.color} rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>

                  {/* Question */}
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                      {faq.question}
                    </h3>
                  </div>

                  {/* Chevron */}
                  <div
                    className={`flex-shrink-0 transition-transform duration-300 ${
                      isOpen ? 'rotate-180' : ''
                    }`}
                  >
                    <ChevronDown className="w-6 h-6 text-gray-400 group-hover:text-blue-500" />
                  </div>
                </button>

                {/* Answer */}
                <div
                  className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                  }`}
                >
                  <div className="px-6 pb-6">
                    <div className="pl-16">
                      <div
                        className={`bg-gradient-to-r ${
                          faq.color
                        } bg-opacity-5 rounded-xl p-4 border-l-4 border-gradient-to-b ${faq.color
                          .replace('from-', 'border-')
                          .replace(' to-cyan-600', '')
                          .replace(' to-pink-600', '')
                          .replace(' to-emerald-600', '')
                          .replace(' to-red-600', '')}`}
                      >
                        <p className="text-gray-700 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Additional Help Section */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Our support team is here to help you get the most out of our AI
              tools.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105">
                Contact Support
              </button>
              <button className="border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-600 px-8 py-3 rounded-xl font-semibold transition-all duration-300">
                View Documentation
              </button>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <Shield className="w-6 h-6 text-green-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">
              100% Secure
            </span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <Zap className="w-6 h-6 text-blue-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">
              Lightning Fast
            </span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">Top Rated</span>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <span className="text-sm text-gray-600 font-medium">
              24/7 Available
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}
