'use client'

import { useState, useEffect, useRef } from 'react'
import { Play, Pause, RotateCcw } from 'lucide-react'

interface BackgroundEraseDemoProps {
  beforeImage?: string
  afterImage?: string
  className?: string
}

export function BackgroundEraseDemo({
  beforeImage = 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=600&h=400&fit=crop',
  afterImage = 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=600&h=400&fit=crop&blend=FFFFFF&blend-mode=multiply&blend-alpha=50',
  className = '',
}: BackgroundEraseDemoProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [progress, setProgress] = useState(0)
  const [isHovered, setIsHovered] = useState(false)
  const animationRef = useRef<number>()
  const startTimeRef = useRef<number>()

  const duration = 4000 // 4 seconds for smoother animation

  const animate = (timestamp: number) => {
    if (!startTimeRef.current) {
      startTimeRef.current = timestamp
    }

    const elapsed = timestamp - startTimeRef.current
    const newProgress = Math.min(elapsed / duration, 1)

    // Use requestAnimationFrame for smoother updates
    setProgress(newProgress)

    if (newProgress < 1 && isPlaying) {
      animationRef.current = requestAnimationFrame(animate)
    } else if (newProgress >= 1) {
      setIsPlaying(false)
      // Auto restart after a pause
      setTimeout(() => {
        if (!isHovered) {
          // Only auto-restart if not hovered
          restart()
        }
      }, 2000)
    }
  }

  const play = () => {
    if (!isPlaying) {
      setIsPlaying(true)
      startTimeRef.current = undefined
      animationRef.current = requestAnimationFrame(animate)
    }
  }

  const pause = () => {
    setIsPlaying(false)
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
    }
  }

  const restart = () => {
    setProgress(0)
    setIsPlaying(true)
    startTimeRef.current = undefined
    animationRef.current = requestAnimationFrame(animate)
  }

  useEffect(() => {
    // Auto start animation when component mounts
    const timer = setTimeout(() => {
      play()
    }, 500)

    return () => {
      clearTimeout(timer)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  // Easing function for smooth animation
  const easeInOutCubic = (t: number) => {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2
  }

  const easedProgress = easeInOutCubic(progress)
  const clipPath = `inset(0 ${100 - easedProgress * 100}% 0 0)`

  return (
    <div
      className={`relative group ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Container */}
      <div className="relative w-full h-80 rounded-2xl overflow-hidden shadow-2xl bg-gray-100">
        {/* Before Image */}
        <div className="absolute inset-0">
          <img
            src={beforeImage}
            alt="Before - with background"
            className="w-full h-full object-cover"
          />
          {/* Overlay to show it's the "before" state */}
          <div className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
            Before
          </div>
        </div>

        {/* After Image */}
        <div
          className="absolute inset-0 transition-all duration-100"
          style={{ clipPath }}
        >
          <img
            src={afterImage}
            alt="After - background removed"
            className="w-full h-full object-cover"
          />
          {/* Overlay to show it's the "after" state */}
          <div className="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
            After
          </div>
        </div>

        {/* Vertical Line Indicator */}
        <div
          className="absolute top-0 bottom-0 w-1 bg-white shadow-lg transition-all duration-100 z-10"
          style={{
            left: `${easedProgress * 100}%`,
            transform: 'translateX(-50%)',
            boxShadow: '0 0 20px rgba(255, 255, 255, 0.8)',
          }}
        >
          {/* Line handle */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-blue-500 flex items-center justify-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          </div>
        </div>

        {/* Progress indicator */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-black/50 backdrop-blur-sm rounded-full p-2">
            <div className="w-full bg-gray-300 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-100"
                style={{ width: `${progress * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Control buttons */}
        <div
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-opacity duration-300 ${
            isHovered ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <div className="flex items-center gap-2 bg-black/70 backdrop-blur-sm rounded-full p-2">
            <button
              onClick={isPlaying ? pause : play}
              className="w-10 h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors duration-200"
            >
              {isPlaying ? (
                <Pause className="w-5 h-5 text-gray-700" />
              ) : (
                <Play className="w-5 h-5 text-gray-700 ml-0.5" />
              )}
            </button>
            <button
              onClick={restart}
              className="w-10 h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors duration-200"
            >
              <RotateCcw className="w-5 h-5 text-gray-700" />
            </button>
          </div>
        </div>

        {/* Sparkle effects */}
        {isPlaying && (
          <>
            <div
              className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping"
              style={{
                left: `${easedProgress * 100}%`,
                top: '20%',
                transform: 'translateX(-50%)',
                animationDelay: '0s',
              }}
            ></div>
            <div
              className="absolute w-1 h-1 bg-blue-400 rounded-full animate-ping"
              style={{
                left: `${easedProgress * 100}%`,
                top: '60%',
                transform: 'translateX(-50%)',
                animationDelay: '0.5s',
              }}
            ></div>
            <div
              className="absolute w-1.5 h-1.5 bg-green-400 rounded-full animate-ping"
              style={{
                left: `${easedProgress * 100}%`,
                top: '80%',
                transform: 'translateX(-50%)',
                animationDelay: '1s',
              }}
            ></div>
          </>
        )}
      </div>

      {/* Description */}
      <div className="mt-4 text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          AI Background Removal in Action
        </h3>
        <p className="text-sm text-gray-600">
          Watch as our AI intelligently removes the background while preserving
          the subject
        </p>
      </div>
    </div>
  )
}
