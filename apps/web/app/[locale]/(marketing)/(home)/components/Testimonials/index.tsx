'use client'

import { useState } from 'react'
import { Star, Quote, User, Briefcase, Camera, ShoppingBag } from 'lucide-react'

export function Testimonials() {
  const [hoveredTestimonial, setHoveredTestimonial] = useState<number | null>(null)

  const testimonials = [
    {
      id: 1,
      text: "This is the best ai watermark remover I've ever used. It's incredibly fast and the quality is amazing. My workflow is 50% faster now!",
      author: "<PERSON>",
      role: "Content Creator",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=100&h=100&fit=crop&crop=face",
      icon: Camera,
      rating: 5,
      color: "from-blue-500 to-cyan-600"
    },
    {
      id: 2,
      text: "I needed a reliable tiktok watermark remover for my social media posts, and removeAI delivered perfectly. It's so simple and effective.",
      author: "<PERSON>",
      role: "Social Media Manager",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      icon: User,
      rating: 5,
      color: "from-purple-500 to-pink-600"
    },
    {
      id: 3,
      text: "The ability to remove people from photos saved my vacation pictures! I thought they were ruined, but this tool cleaned them up beautifully.",
      author: "<PERSON> <PERSON>",
      role: "Travel Blogger",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
      icon: Camera,
      rating: 5,
      color: "from-green-500 to-emerald-600"
    },
    {
      id: 4,
      text: "As a small business owner, the ai background remover is a lifesaver for my product photos. Professional results without the high cost.",
      author: "David Kim",
      role: "E-commerce Owner",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      icon: ShoppingBag,
      rating: 5,
      color: "from-orange-500 to-red-600"
    }
  ]

  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            What Our Users Are Saying
          </h2>
          <div className="flex items-center justify-center gap-2 mb-4">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className="w-8 h-8 text-yellow-400 fill-current" />
            ))}
            <span className="text-2xl font-bold text-gray-700 ml-2">4.9/5</span>
          </div>
          <p className="text-lg text-gray-600">Based on 10,000+ reviews from satisfied users worldwide</p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {testimonials.map((testimonial) => {
            const IconComponent = testimonial.icon
            const isHovered = hoveredTestimonial === testimonial.id
            
            return (
              <div
                key={testimonial.id}
                className="group relative bg-gradient-to-br from-gray-50 to-white rounded-3xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 cursor-pointer"
                onMouseEnter={() => setHoveredTestimonial(testimonial.id)}
                onMouseLeave={() => setHoveredTestimonial(null)}
              >
                {/* Quote Icon */}
                <div className="absolute -top-3 -left-3 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                  <Quote className="w-4 h-4 text-white" />
                </div>

                {/* Rating */}
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>

                {/* Testimonial Text */}
                <p className="text-gray-700 leading-relaxed mb-6 text-sm">
                  "{testimonial.text}"
                </p>

                {/* Author Info */}
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md"
                    />
                    <div className={`absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r ${testimonial.color} rounded-full flex items-center justify-center shadow-md ${isHovered ? 'scale-110' : ''} transition-transform duration-300`}>
                      <IconComponent className="w-3 h-3 text-white" />
                    </div>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 text-sm">{testimonial.author}</p>
                    <p className="text-xs text-gray-500">{testimonial.role}</p>
                  </div>
                </div>

                {/* Hover Effect Background */}
                <div className={`absolute inset-0 bg-gradient-to-br ${testimonial.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl pointer-events-none`}></div>

                {/* Floating Animation */}
                <div className={`absolute top-4 right-4 transition-all duration-300 ${isHovered ? 'scale-110 rotate-12' : ''}`}>
                  <div className="w-8 h-8 bg-white/80 rounded-full flex items-center justify-center shadow-md">
                    <IconComponent className={`w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300`} />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">10M+</div>
            <div className="text-sm text-gray-600">Images Processed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">50%</div>
            <div className="text-sm text-gray-600">Faster Workflow</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">4.9★</div>
            <div className="text-sm text-gray-600">User Rating</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">24/7</div>
            <div className="text-sm text-gray-600">Available</div>
          </div>
        </div>
      </div>
    </section>
  )
}
