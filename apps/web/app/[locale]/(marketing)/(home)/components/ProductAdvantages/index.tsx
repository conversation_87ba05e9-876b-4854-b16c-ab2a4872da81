'use client'

import { useState } from 'react'
import { ArrowRight, Shield, Zap, Cloud, Star, CheckCircle } from 'lucide-react'
import { useTranslations } from 'next-intl'

export function ProductAdvantages() {
  const t = useTranslations('home')
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('productAdvantagesTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            {t('productAdvantagesDescription')}
          </p>
        </div>

        {/* Advantages Grid */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* First Advantage */}
          <div
            className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-blue-200"
            onMouseEnter={() => setHoveredCard(1)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <div className="flex items-start gap-6">
              <div className="flex-shrink-0">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Shield className="w-8 h-8 text-white" />
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                  {t('advantage1Title')}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-6">
                  {t('advantage1Description')}
                </p>

                {/* Features List */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">
                      {t('advantage1Feature1')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">
                      {t('advantage1Feature2')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">
                      {t('advantage1Feature3')}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Hover Animation */}
            <div
              className={`mt-6 overflow-hidden transition-all duration-500 ${
                hoveredCard === 1 ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'
              }`}
            >
              <div className="flex items-center gap-2 text-blue-600 font-medium">
                <Zap className="w-4 h-4" />
                <span className="text-sm">Lightning-fast AI processing</span>
              </div>
            </div>
          </div>

          {/* Second Advantage */}
          <div
            className="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-purple-200"
            onMouseEnter={() => setHoveredCard(2)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <div className="flex items-start gap-6">
              <div className="flex-shrink-0">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Star className="w-8 h-8 text-white" />
                </div>
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                  More Than a Filter: Advanced BG Blur and Object Removal
                </h3>
                <p className="text-gray-700 leading-relaxed mb-6">
                  Our ai background remover goes beyond simple edits. Add a
                  professional bg blur to portraits or use our advanced AI to
                  seamlessly clean up image files by removing unwanted objects.
                  It's a comprehensive tool for perfect results.
                </p>

                {/* Features List */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">
                      Professional BG blur effects
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">
                      Advanced object removal
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">
                      Comprehensive editing suite
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Hover Animation */}
            <div
              className={`mt-6 overflow-hidden transition-all duration-500 ${
                hoveredCard === 2 ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'
              }`}
            >
              <div className="flex items-center gap-2 text-purple-600 font-medium">
                <Cloud className="w-4 h-4" />
                <span className="text-sm">
                  AI-powered comprehensive editing
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <button className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-5 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-4 mx-auto">
            Try for Free Now
            <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" />
          </button>
          <p className="text-sm text-gray-500 mt-4">
            No registration required • Process unlimited images
          </p>
        </div>
      </div>
    </section>
  )
}
