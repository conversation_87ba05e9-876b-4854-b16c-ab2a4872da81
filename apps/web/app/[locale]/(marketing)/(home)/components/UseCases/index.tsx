'use client'

import { useState } from 'react'
import {
  ArrowRight,
  Play,
  ShoppingBag,
  Users,
  Camera,
  Sparkles,
} from 'lucide-react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

export function UseCases() {
  const t = useTranslations('home')
  const [hoveredCase, setHoveredCase] = useState<number | null>(null)

  const useCases = [
    {
      id: 1,
      icon: Play,
      title: 'Perfect for Creators: The Ultimate TikTok Watermark Remover',
      description:
        'With over 5 million watermarks removed, our ai watermark remover is the go-to choice. This powerful watermark deleter ensures a clean, professional look for every post, helping you repurpose content effortlessly and securely.',
      cta: 'Remove Watermark Now',
      image:
        'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=600&h=400&fit=crop',
      stats: '5M+ watermarks removed',
      color: 'from-red-500 to-pink-600',
    },
    {
      id: 2,
      icon: ShoppingBag,
      title: 'E-commerce Ready: Flawless Image Cutout Background',
      description:
        "98% of top e-commerce sellers use clean backgrounds. Our ai background remover provides a perfect image cutout background, boosting sales by up to 30%. Also, easily change image background to fit your brand's style.",
      cta: 'Edit Your Product Photo',
      image:
        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop',
      stats: '30% sales boost',
      color: 'from-blue-500 to-cyan-600',
    },
    {
      id: 3,
      icon: Users,
      title: 'Picture Perfect: Easily Remove People from Photos',
      description:
        "Don't let photobombers ruin your memories. Our AI can remove people from photos seamlessly. This powerful ai background remover also helps you clean up image distractions for a flawless result, all while protecting your private photos.",
      cta: 'Clean Up Your Photo',
      image:
        'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=600&h=400&fit=crop',
      stats: 'Seamless removal',
      color: 'from-green-500 to-emerald-600',
    },
    {
      id: 4,
      icon: Camera,
      title: 'Professional Portraits with a Single Click: Add BG Blur',
      description:
        'Achieve a stunning DSLR-like effect with our bg blur feature. This is more than a simple filter; our ai watermark remover tool intelligently isolates the subject for a flawless, artistic finish that makes your portraits pop.',
      cta: 'Blur Your Background',
      image:
        'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=600&h=400&fit=crop',
      stats: 'DSLR-like quality',
      color: 'from-purple-500 to-indigo-600',
    },
  ]

  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('useCasesTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            {t('useCasesDescription')}
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="space-y-16">
          {useCases.map((useCase, index) => {
            const isEven = index % 2 === 0
            const IconComponent = useCase.icon

            return (
              <div
                key={useCase.id}
                className={`grid lg:grid-cols-2 gap-12 items-center ${
                  !isEven ? 'lg:grid-flow-col-dense' : ''
                }`}
                onMouseEnter={() => setHoveredCase(useCase.id)}
                onMouseLeave={() => setHoveredCase(null)}
              >
                {/* Image Side */}
                <div
                  className={`${
                    !isEven ? 'lg:col-start-2' : ''
                  } relative group`}
                >
                  <div className="relative rounded-3xl overflow-hidden shadow-2xl">
                    <Image
                      src={useCase.image}
                      alt={useCase.title}
                      width={600}
                      height={400}
                      className="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110"
                    />

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-6 left-6 right-6">
                        <div className="flex items-center gap-2 text-white font-semibold">
                          <Sparkles className="w-5 h-5" />
                          <span>{useCase.stats}</span>
                        </div>
                      </div>
                    </div>

                    {/* Floating Icon */}
                    <div
                      className={`absolute top-6 right-6 w-12 h-12 bg-gradient-to-r ${
                        useCase.color
                      } rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 ${
                        hoveredCase === useCase.id ? 'scale-110 rotate-12' : ''
                      }`}
                    >
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                {/* Content Side */}
                <div className={`${!isEven ? 'lg:col-start-1' : ''} space-y-6`}>
                  <div className="space-y-4">
                    <h3 className="text-3xl font-bold text-gray-900 leading-tight">
                      {useCase.title}
                    </h3>
                    <p className="text-lg text-gray-700 leading-relaxed">
                      {useCase.description}
                    </p>
                  </div>

                  <button
                    className={`group bg-gradient-to-r ${useCase.color} hover:shadow-2xl text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 flex items-center gap-3`}
                  >
                    {useCase.cta}
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
