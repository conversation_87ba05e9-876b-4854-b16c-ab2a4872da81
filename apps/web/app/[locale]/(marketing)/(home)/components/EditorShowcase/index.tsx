'use client'

import { useState } from 'react'
import { Sparkles } from 'lucide-react'

export function EditorShowcase() {
  const [hoveredAward, setHoveredAward] = useState<number | null>(null)

  const awards = [
    {
      id: 1,
      icon: '🍎',
      title: 'Apple',
      subtitle: "Editors' Choice",
    },
    {
      id: 2,
      icon: '📱',
      title: '300+ million',
      subtitle: 'downloads',
    },
    {
      id: 3,
      icon: '🏆',
      title: 'Google Play',
      subtitle: "Editors' Choice",
    },
  ]

  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <Sparkles className="w-4 h-4" />
            Trusted by Millions
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Award-Winning AI Technology
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Join millions of users who trust our AI-powered image editing tools.
            Recognized by industry leaders and loved by creators worldwide.
          </p>
        </div>

        {/* Awards Showcase - Horizontal Layout like the image */}
        <div className="bg-gray-50 rounded-2xl p-8 lg:p-12">
          <div className="flex flex-col lg:flex-row items-center justify-center gap-8 lg:gap-16">
            {awards.map((award, index) => {
              const isHovered = hoveredAward === award.id

              return (
                <div
                  key={award.id}
                  className="group relative flex flex-col items-center"
                  onMouseEnter={() => setHoveredAward(award.id)}
                  onMouseLeave={() => setHoveredAward(null)}
                >
                  {/* Laurel Wreath Container */}
                  <div className="relative">
                    {/* Left Laurel */}
                    <div className="absolute -left-8 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <svg width="24" height="32" viewBox="0 0 24 32" fill="currentColor">
                        <path d="M2 16c0-4 2-8 6-10 2 2 4 6 4 10s-2 8-4 10c-4-2-6-6-6-10z"/>
                        <path d="M4 12c1-1 2-1 3 0 0 1-1 2-2 2s-2-1-1-2z"/>
                        <path d="M4 20c1 1 2 1 3 0 0-1-1-2-2-2s-2 1-1 2z"/>
                      </svg>
                    </div>

                    {/* Right Laurel */}
                    <div className="absolute -right-8 top-1/2 transform -translate-y-1/2 text-gray-400 scale-x-[-1]">
                      <svg width="24" height="32" viewBox="0 0 24 32" fill="currentColor">
                        <path d="M2 16c0-4 2-8 6-10 2 2 4 6 4 10s-2 8-4 10c-4-2-6-6-6-10z"/>
                        <path d="M4 12c1-1 2-1 3 0 0 1-1 2-2 2s-2-1-1-2z"/>
                        <path d="M4 20c1 1 2 1 3 0 0-1-1-2-2-2s-2 1-1 2z"/>
                      </svg>
                    </div>

                    {/* Award Content */}
                    <div className="text-center px-8">
                      {/* Icon */}
                      <div className="text-4xl mb-3 transform transition-transform duration-300 group-hover:scale-110">
                        {award.icon}
                      </div>

                      {/* Title */}
                      <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">
                        {award.title}
                      </h3>

                      {/* Subtitle */}
                      <p className="text-sm font-medium text-gray-600">
                        {award.subtitle}
                      </p>
                    </div>
                  </div>

                  {/* Hover effect */}
                  <div className={`absolute inset-0 bg-blue-50 opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-xl pointer-events-none ${isHovered ? 'scale-110' : ''}`}></div>
                </div>
              )
            })}
          </div>

            {/* Bottom stats */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-blue-600">4.9★</div>
                  <div className="text-sm text-gray-600">App Store Rating</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-green-600">300M+</div>
                  <div className="text-sm text-gray-600">Downloads</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-purple-600">50M+</div>
                  <div className="text-sm text-gray-600">Active Users</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-orange-600">150+</div>
                  <div className="text-sm text-gray-600">Countries</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="mt-16 text-center">
          <p className="text-sm text-gray-500 mb-6">
            Trusted by leading companies and creators worldwide
          </p>
          <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
            {/* Placeholder for company logos */}
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Adobe</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Canva</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Figma</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">
                Shopify
              </span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Etsy</span>
            </div>
          </div>
        </div>

        {/* Call to action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Join the millions who trust our AI technology
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Experience the same award-winning technology that has earned
              recognition from Apple, Google, and millions of users worldwide.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-300 transform hover:scale-105">
              Try It Free Now
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
