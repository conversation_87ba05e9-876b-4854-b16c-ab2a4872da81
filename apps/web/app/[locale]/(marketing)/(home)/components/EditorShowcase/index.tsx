'use client'

import { useState } from 'react'
import { Award, Download, Users, Sparkles } from 'lucide-react'

export function EditorShowcase() {
  const [hoveredAward, setHoveredAward] = useState<number | null>(null)

  const awards = [
    {
      id: 1,
      icon: Award,
      title: 'Apple',
      subtitle: "Editors' Choice",
      description: '300+ million downloads',
      color: 'from-gray-600 to-gray-800',
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
    },
    {
      id: 2,
      icon: Download,
      title: '300+ million',
      subtitle: 'downloads',
      description: 'Trusted worldwide',
      color: 'from-blue-500 to-blue-700',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-800',
    },
    {
      id: 3,
      icon: Award,
      title: 'Google Play',
      subtitle: "Editors' Choice",
      description: 'Top rated app',
      color: 'from-green-500 to-green-700',
      bgColor: 'bg-green-50',
      textColor: 'text-green-800',
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-white to-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <Sparkles className="w-4 h-4" />
            Trusted by Millions
          </div>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Award-Winning AI Technology
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Join millions of users who trust our AI-powered image editing tools.
            Recognized by industry leaders and loved by creators worldwide.
          </p>
        </div>

        {/* Awards Showcase */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-3xl opacity-50"></div>

          <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-12 shadow-2xl border border-gray-100">
            {/* Decorative elements */}
            <div className="absolute top-6 left-6 w-20 h-20 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-20 animate-pulse"></div>
            <div
              className="absolute bottom-6 right-6 w-16 h-16 bg-gradient-to-br from-blue-200 to-purple-200 rounded-full opacity-20 animate-pulse"
              style={{ animationDelay: '1s' }}
            ></div>

            <div className="grid lg:grid-cols-3 gap-8 items-center">
              {awards.map((award, index) => {
                const IconComponent = award.icon
                const isHovered = hoveredAward === award.id

                return (
                  <div
                    key={award.id}
                    className="group relative"
                    onMouseEnter={() => setHoveredAward(award.id)}
                    onMouseLeave={() => setHoveredAward(null)}
                  >
                    {/* Award Card */}
                    <div
                      className={`relative ${
                        award.bgColor
                      } rounded-2xl p-8 text-center transition-all duration-500 transform ${
                        isHovered ? 'scale-105 shadow-2xl' : 'shadow-lg'
                      } border border-gray-100`}
                    >
                      {/* Laurel decorations */}
                      <div className="absolute top-4 left-4 text-2xl opacity-30">
                        🏆
                      </div>
                      <div className="absolute top-4 right-4 text-2xl opacity-30">
                        🏆
                      </div>

                      {/* Icon */}
                      <div
                        className={`w-16 h-16 bg-gradient-to-r ${award.color} rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>

                      {/* Content */}
                      <div className="space-y-2">
                        <h3
                          className={`text-xl font-bold ${award.textColor} group-hover:scale-105 transition-transform duration-300`}
                        >
                          {award.title}
                        </h3>
                        <p
                          className={`text-sm font-semibold ${award.textColor} opacity-80`}
                        >
                          {award.subtitle}
                        </p>
                        <p className="text-xs text-gray-600 mt-2">
                          {award.description}
                        </p>
                      </div>

                      {/* Hover effect background */}
                      <div
                        className={`absolute inset-0 bg-gradient-to-br ${award.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-2xl pointer-events-none`}
                      ></div>
                    </div>

                    {/* Floating animation for middle card */}
                    {index === 1 && (
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce shadow-lg">
                        <Sparkles className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                )
              })}
            </div>

            {/* Bottom stats */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-blue-600">4.9★</div>
                  <div className="text-sm text-gray-600">App Store Rating</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-green-600">300M+</div>
                  <div className="text-sm text-gray-600">Downloads</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-purple-600">50M+</div>
                  <div className="text-sm text-gray-600">Active Users</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-orange-600">150+</div>
                  <div className="text-sm text-gray-600">Countries</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="mt-16 text-center">
          <p className="text-sm text-gray-500 mb-6">
            Trusted by leading companies and creators worldwide
          </p>
          <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
            {/* Placeholder for company logos */}
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Adobe</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Canva</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Figma</span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">
                Shopify
              </span>
            </div>
            <div className="w-24 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500 font-semibold">Etsy</span>
            </div>
          </div>
        </div>

        {/* Call to action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Join the millions who trust our AI technology
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Experience the same award-winning technology that has earned
              recognition from Apple, Google, and millions of users worldwide.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors duration-300 transform hover:scale-105">
              Try It Free Now
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
