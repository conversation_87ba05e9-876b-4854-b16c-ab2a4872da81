import './home-gradient-border.css'
import './scroll-animations.css'
import { HeroSection } from './components/HeroSection'
import { ProductAdvantages } from './components/ProductAdvantages'
import { UseCases } from './components/UseCases'
import { HowToGuide } from './components/HowToGuide'
import { Testimonials } from './components/Testimonials'
import { FAQ } from './components/FAQ'
import { BackgroundEraseDemo } from './components/BackgroundEraseDemo'
import { ObjectRemovalDemo } from './components/ObjectRemovalDemo'
import { EditorShowcase } from './components/EditorShowcase'

export async function generateMetadata() {
  return {
    title:
      'The Ultimate AI Watermark Remover & AI Background Remover | removeAI',
    description:
      'Use our powerful ai watermark remover and ai background remover to instantly clean up images or add a professional bg blur. The best watermark remover free tool for your photos.',
    keywords:
      'ai watermark remover, ai background remover, bg blur, remove watermark from photo, watermark remover free, tiktok watermark remover, remove people from photos, clean up image',
  }
}

const Index = async () => {
  return (
    <div className="w-full min-h-screen text-gray-900">
      {/* Hero Section */}
      <HeroSection />

      {/* Product Advantages Section */}
      <ProductAdvantages />

      {/* Demo Section with Background Erase and Object Removal */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              See Our AI in Action
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
              Watch how our advanced AI technology transforms your images with
              precision and speed
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            <BackgroundEraseDemo />
            <ObjectRemovalDemo />
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <UseCases />

      {/* Editor Showcase */}
      <EditorShowcase />

      {/* How To Guide Section */}
      <HowToGuide />

      {/* Testimonials Section */}
      <Testimonials />

      {/* FAQ Section */}
      <FAQ />
    </div>
  )
}

export default Index
