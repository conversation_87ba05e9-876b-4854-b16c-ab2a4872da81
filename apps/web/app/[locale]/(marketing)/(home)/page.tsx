import './home-gradient-border.css'
import './scroll-animations.css'
import { HeroSection } from './components/HeroSection'
import { ProductAdvantages } from './components/ProductAdvantages'
import { UseCases } from './components/UseCases'
import { HowToGuide } from './components/HowToGuide'
import { Testimonials } from './components/Testimonials'
import { FAQ } from './components/FAQ'
import { BackgroundEraseDemo } from './components/BackgroundEraseDemo'
import { ObjectRemovalDemo } from './components/ObjectRemovalDemo'
import { EditorShowcase } from './components/EditorShowcase'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations('home')
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
  }
}

const Index = async () => {
  const t = await getTranslations('home')

  return (
    <div className="w-full min-h-screen text-gray-900">
      {/* Hero Section */}
      <HeroSection />

      {/* Product Advantages Section */}
      <ProductAdvantages />

      {/* Demo Section with Background Erase and Object Removal */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              {t('demoSectionTitle')}
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
              {t('demoSectionDescription')}
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            <BackgroundEraseDemo />
            <ObjectRemovalDemo />
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <UseCases />

      {/* Editor Showcase */}
      <EditorShowcase />

      {/* How To Guide Section */}
      <HowToGuide />

      {/* Testimonials Section */}
      <Testimonials />

      {/* FAQ Section */}
      <FAQ />
    </div>
  )
}

export default Index
