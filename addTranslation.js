const fs = require('fs')
const path = require('path')

const translations = {
  en: {
    home: {
      // page.tsx metadata
      title:
        'The Ultimate AI Watermark Remover & AI Background Remover | removeAI',
      description:
        'Use our powerful ai watermark remover and ai background remover to instantly clean up images or add a professional bg blur. The best watermark remover free tool for your photos.',
      keywords:
        'ai watermark remover, ai background remover, bg blur, remove watermark from photo, watermark remover free, tiktok watermark remover, remove people from photos, clean up image',

      // page.tsx demo section
      demoSectionTitle: 'See Our AI in Action',
      demoSectionDescription:
        'Watch how our advanced AI technology transforms your images with precision and speed',

      // HeroSection
      heroTitle: 'Free & Effortless',
      heroTitleBlue: 'AI Watermark Remover',
      heroTitlePurple: 'AI Background Remover',
      heroDescription:
        'Experience the best ai watermark remover available. Quickly remove watermark from photo with our simple, efficient, and secure tool, ensuring your privacy is always protected.',
      uploadImage: 'Upload Image',
      processing: 'Processing...',
      freeSecure: 'Free & Secure',
      noRegistration: 'No Registration',
      instantResults: 'Instant Results',
      dragDropImage: 'Drag & Drop Your Image',
      supportFormats: 'Support JPG, PNG, WEBP formats',
      maxFileSize: 'Maximum file size: 10MB',
      aiPoweredProcessing: 'AI-Powered Processing',
      noImageTryExamples: 'No image? Try these examples:',
      privacyProtected: '🔒 Your privacy is protected',
      imagesDeleted:
        'Images are processed securely and automatically deleted after 24 hours.',
      dropImageHere: 'Drop image here',
      releaseToUpload: 'Release to upload and start editing',
      exampleAlt: 'Example',

      // ProductAdvantages
      productAdvantagesTitle: 'Why Our AI Background Remover Stands Out',
      productAdvantagesDescription:
        'Discover the power of our ai background remover. We offer batch remove background capabilities, saving you hours of manual work. Efficiency and simplicity are built into our core.',
      advantage1Title:
        'High-Efficiency AI Watermark Remover with Free, Private Storage',
      advantage1Description:
        'Unlike competitors that limit you, we offer free, secure cloud storage for your edited images. Our system is designed to remove text from image files with unmatched speed, and all uploads are protected by our strict privacy policy.',
      advantage1Feature1: 'Free secure cloud storage',
      advantage1Feature2: 'Unmatched processing speed',
      advantage1Feature3: 'Strict privacy protection',
      advantage1Hover: 'Lightning-fast AI processing',
      advantage2Title:
        'More Than a Filter: Advanced BG Blur and Object Removal',
      advantage2Description:
        "Our ai background remover goes beyond simple edits. Add a professional bg blur to portraits or use our advanced AI to seamlessly clean up image files by removing unwanted objects. It's a comprehensive tool for perfect results.",
      advantage2Feature1: 'Professional BG blur effects',
      advantage2Feature2: 'Advanced object removal',
      advantage2Feature3: 'Comprehensive editing suite',
      advantage2Hover: 'AI-powered comprehensive editing',
      tryForFreeNow: 'Try for Free Now',
      noRegistrationRequired:
        'No registration required • Process unlimited images',

      // UseCases
      useCasesTitle: 'Real-World Applications of Our AI Tools',
      useCasesDescription:
        "See how our ai watermark remover helps creators and businesses. It's the ultimate tool to remove object from photo and enhance your visual content with professional quality.",
      useCase1Title:
        'Perfect for Creators: The Ultimate TikTok Watermark Remover',
      useCase1Description:
        'With over 5 million watermarks removed, our ai watermark remover is the go-to choice. This powerful watermark deleter ensures a clean, professional look for every post, helping you repurpose content effortlessly and securely.',
      useCase1Cta: 'Remove Watermark Now',
      useCase1Stats: '5M+ watermarks removed',
      useCase2Title: 'E-commerce Ready: Flawless Image Cutout Background',
      useCase2Description:
        "98% of top e-commerce sellers use clean backgrounds. Our ai background remover provides a perfect image cutout background, boosting sales by up to 30%. Also, easily change image background to fit your brand's style.",
      useCase2Cta: 'Edit Your Product Photo',
      useCase2Stats: '30% sales boost',
      useCase3Title: 'Picture Perfect: Easily Remove People from Photos',
      useCase3Description:
        "Don't let photobombers ruin your memories. Our AI can remove people from photos seamlessly. This powerful ai background remover also helps you clean up image distractions for a flawless result, all while protecting your private photos.",
      useCase3Cta: 'Clean Up Your Photo',
      useCase3Stats: 'Seamless removal',
      useCase4Title: 'Professional Portraits with a Single Click: Add BG Blur',
      useCase4Description:
        'Achieve a stunning DSLR-like effect with our bg blur feature. This is more than a simple filter; our ai watermark remover tool intelligently isolates the subject for a flawless, artistic finish that makes your portraits pop.',
      useCase4Cta: 'Blur Your Background',
      useCase4Stats: 'DSLR-like quality',

      // HowToGuide
      howToGuideTitle: 'How to Use Our AI Watermark Remover',
      howToGuideDescription:
        "In just two simple steps, use our ai watermark remover to get a perfect image. It's the most efficient watermark remover free of charge you'll find online.",
      step1Title: 'Step 1: Upload Your Image',
      step1Description:
        'Simply drag and drop your file or select it from your device. Our tool is designed for high efficiency and supports various formats to help you remove watermark from photo instantly.',
      step1Feature1: 'Drag & drop support',
      step1Feature2: 'Multiple format support',
      step1Feature3: 'Instant processing start',
      step1Feature4: 'Secure upload',
      step2Title: 'Step 2: Download Your Cleaned Image',
      step2Description:
        "Our ai background remover processes your image in seconds. Your final image is ready for download, with an optional bg blur effect available before you save. It's that simple and fast.",
      step2Feature1: 'Lightning-fast processing',
      step2Feature2: 'Optional BG blur',
      step2Feature3: 'High-quality output',
      step2Feature4: 'Instant download',
      upload: 'Upload',
      process: 'Process',
      download: 'Download',
      averageProcessingTime: 'Average processing time: 3-5 seconds',
      startEditingNowForFree: 'Start Editing Now for Free',
      noAccountNeeded: 'No account needed • Unlimited usage • Instant results',

      // Testimonials
      testimonialsTitle: 'What Our Users Are Saying',
      testimonialsRating: '4.9/5',
      testimonialsReviewsCount:
        'Based on 10,000+ reviews from satisfied users worldwide',
      testimonial1Text:
        "This is the best ai watermark remover I've ever used. It's incredibly fast and the quality is amazing. My workflow is 50% faster now!",
      testimonial1Author: 'Sarah Chen',
      testimonial1Role: 'Content Creator',
      testimonial2Text:
        "I needed a reliable tiktok watermark remover for my social media posts, and removeAI delivered perfectly. It's so simple and effective.",
      testimonial2Author: 'Marcus Johnson',
      testimonial2Role: 'Social Media Manager',
      testimonial3Text:
        'The ability to remove people from photos saved my vacation pictures! I thought they were ruined, but this tool cleaned them up beautifully.',
      testimonial3Author: 'Emily Rodriguez',
      testimonial3Role: 'Travel Blogger',
      testimonial4Text:
        'As a small business owner, the ai background remover is a lifesaver for my product photos. Professional results without the high cost.',
      testimonial4Author: 'David Kim',
      testimonial4Role: 'E-commerce Owner',
      testimonialsStatsImages: '10M+',
      testimonialsStatsImagesLabel: 'Images Processed',
      testimonialsStatsFaster: '50%',
      testimonialsStatsFasterLabel: 'Faster Workflow',
      testimonialsStatsRating: '4.9★',
      testimonialsStatsRatingLabel: 'User Rating',
      testimonialsStatsAvailable: '24/7',
      testimonialsStatsAvailableLabel: 'Available',

      // FAQ
      faqTitle: 'Frequently Asked Questions',
      faqDescription:
        'Everything you need to know about our AI-powered image editing tools',
      faq1Question:
        'How does the free watermark remover work while ensuring privacy?',
      faq1Answer:
        'Our ai watermark remover processes images on secure, encrypted servers and automatically deletes all data after 24 hours. We prioritize user privacy, so you can edit with peace of mind.',
      faq2Question: 'Can I really remove text from an image with this tool?',
      faq2Answer:
        'Yes, our advanced ai background remover algorithm can identify and reconstruct the area behind text overlays, helping you clean up image files for any use case without leaving artifacts.',
      faq3Question:
        'Is your ai watermark remover better than other online tools?',
      faq3Answer:
        'Our tool offers a unique combination of speed, accuracy, and free features. You can even add a professional bg blur, making it a versatile all-in-one editor for all your needs.',
      faq4Question: 'What is the process to remove a watermark from a photo?',
      faq4Answer:
        'Simply upload your image, and our ai watermark remover automatically detects and erases the watermark. The process is fully automated for maximum efficiency, giving you a clean image in seconds.',
      stillHaveQuestions: 'Still have questions?',
      supportTeamHelp:
        'Our support team is here to help you get the most out of our AI tools.',
      contactSupport: 'Contact Support',
      viewDocumentation: 'View Documentation',
      secureLabel: '100% Secure',
      lightningFastLabel: 'Lightning Fast',
      topRatedLabel: 'Top Rated',
      availableLabel: '24/7 Available',

      // EditorShowcase
      editorShowcaseTitle: 'Award-Winning AI Technology',
      editorShowcaseDescription:
        'Join millions of users who trust our AI-powered image editing tools. Recognized by industry leaders and loved by creators worldwide.',
      trustedByMillions: 'Trusted by Millions',
      appleEditorsChoice: 'Apple',
      appleEditorsChoiceSubtitle: "Editors' Choice",
      millionDownloads: '300+ million',
      millionDownloadsSubtitle: 'downloads',
      googlePlayEditorsChoice: 'Google Play',
      googlePlayEditorsChoiceSubtitle: "Editors' Choice",
      appStoreRating: '4.9★',
      appStoreRatingLabel: 'App Store Rating',
      downloadsLabel: 'Downloads',
      activeUsersLabel: 'Active Users',
      countriesLabel: 'Countries',
      joinMillions: 'Join the millions who trust our AI technology',
      joinMillionsDescription:
        'Experience the same award-winning technology that has earned recognition from Apple, Google, and millions of users worldwide.',
      tryItFreeNow: 'Try It Free Now',

      // BackgroundEraseDemo
      backgroundEraseDemoTitle: 'AI Background Removal in Action',
      backgroundEraseDemoDescription:
        'Watch as our AI intelligently removes the background while preserving the subject',
      before: 'Before',
      after: 'After',

      // ObjectRemovalDemo
      objectRemovalDemoTitle: 'Smart Object Removal',
      objectRemovalDemoDescription:
        'Simply mark unwanted objects and watch our AI intelligently remove them',
      ready: 'Ready',
      markingObjects: 'Marking Objects',
      aiProcessing: 'AI Processing...',
      cleaned: '✨ Cleaned!',
      clickToStartDemo: 'Click to start demo',
      markingUnwantedObjects: 'Marking unwanted objects...',
      aiRemovingObjects: 'AI removing objects...',
      objectsSuccessfullyRemoved: 'Objects successfully removed!',
    },
    oldfilter: {
      // page.tsx
      title:
        'AI Age Progression Free Online | Try Aging Filter to See Your Future Face',
      description:
        "See how you'll look when you're older with our AI age progression free online tool. Try our realistic age filter and aging filter in seconds — free, no signup, no watermark.",
      keywords:
        "AI age progression, AI age progression free online, age filter, aging filter, age filter free online, aging filter free, photo aged, old filter, old age filter, face ager, aging ai, photo aging, age a photo, face aging online free, old man filter, old person filter, age changer, AI old, face old, what will I look like when I'm older",
      openGraphTitle:
        'AI Age Progression Free Online | Try Aging Filter to See Your Future Face',
      openGraphDescription:
        "See how you'll look when you're older with our AI age progression free online tool. Try our realistic age filter and aging filter in seconds — free, no signup, no watermark.",
      twitterTitle:
        'AI Age Progression Free Online | Try Aging Filter to See Your Future Face',
      twitterDescription:
        "See how you'll look when you're older with our AI age progression free online tool. Try our realistic age filter and aging filter in seconds — free, no signup, no watermark.",
      pageTitle:
        'AI Age Progression Free Online – Instantly See Your Future Face',
      pageDescription:
        "See how you'll look as you age with our AI age progression free online tool. Upload a photo and get a realistic photo aged result in seconds using our advanced AI age filter and aging filter.",
      uploadButton: 'Upload and Try Age Filter Now',
      // WhyUs.tsx
      whyUsTitle: 'Why Choose Our Age Progression Tool?',
      // TestimonialSection.tsx
      testimonialTitle: 'What Our Users Say',
      testimonialDescription:
        "Thousands have used our AI age progression free online tool to see their future selves. Here's what they're saying:",
      // TargetAudienceSection.tsx
      targetAudienceTitle: 'Who Can Use Our Face Ager?',
      targetAudienceDescription:
        "Our face ager is made for everyone. Whether you're young or old, using it alone or with loved ones — our AI age progression free online tool helps you explore the beauty of time.",
      // KeyFeaturesSection.tsx
      keyFeaturesTitle: 'Key Features of Our Age Progression Tool',
      keyFeaturesDescription:
        "Our AI age progression free online tool is built to give you a smooth and fun experience. Here's why people love using it:",
      // HowToGuideSection.tsx
      howToTitle: 'How to Use Our AI Age Progression Tool in 3 Simple Steps？',
      howToCta: 'Try Age Filter Now',
      // FAQSection.tsx
      faqTitle: 'Frequently Asked Questions',
      faqDescription:
        "Got questions? Here's what to know about using our AI age progression free online tool.",
      // CaseStudySection.tsx
      caseStudyTitle: 'Real-World Applications',
      // CompareShowcase.tsx
      compareBefore: 'Before',
      compareAfter: 'After',
      // data files
      caseStudy1Alt: 'AI age progression result of a 7-year-old boy',
      caseStudy2Alt: 'AI age progression result of a 20-year-old woman',
      caseStudy3Alt: 'AI age progression result of a couple',
      caseStudy4Alt: 'AI age progression result of a mom and her daughter',
      caseStudy5Alt: 'AI age progression result of friends',
      caseStudy6Alt: 'AI age progression result of a man and his dog',
      faq1Question: 'What is the meaning of age progression?',
      faq1Answer:
        'Age progression means showing what a person might look like as they grow older. <a href="https://imggen.org" target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:text-blue-300 underline transition-colors">Imggen.org</a> - Our AI age progression tool uses facial features and patterns to create a photo aged version of you in seconds. It\'s like seeing your future self — instantly.',
      faq2Question: 'How to get an age progression picture free online?',
      faq2Answer:
        'It\'s easy! Just upload your photo to our AI age progression free online tool - <a href="https://imggen.org" target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:text-blue-300 underline transition-colors">Imggen.org</a>. The aging filter will do the rest. No sign-up, no cost, no watermark — just simple and fast results.',
      faq3Question: 'How do you change your age with TikTok old filter?',
      faq3Answer:
        'On TikTok, people often use the old filter trend. But if you want a more detailed result, try our face aging online free tool - <a href="https://imggen.org" target="_blank" rel="noopener noreferrer" class="text-blue-400 hover:text-blue-300 underline transition-colors">Imggen.org</a>. It gives you a better look at how your face might age with more natural results.',
      faq4Question: 'Is your AI age filter accurate?',
      faq4Answer:
        'While no tool can predict the future perfectly, our AI age filter is trained to simulate real aging effects — like wrinkles, hair changes, and facial shifts. Many users find the aging filter results surprisingly close to reality.',
      faq5Question: 'Will my photos be saved or shared?',
      faq5Answer:
        'No. We respect your privacy. All photos uploaded to our AI age progression platform are processed securely and deleted shortly after. Your photo aged result is yours only — not stored or shared.',
      howTo1Title: 'Upload Your Photo',
      howTo1Description:
        'Start by uploading a clear portrait to our AI age progression tool. Ensure your face is well-lit and centered. Our smart face ager works best with front-facing images.',
      howTo2Title: 'Click "Generate" to Age Your Face',
      howTo2Description:
        'Click the "Generate" button to watch how the old person filter transforms your look! Our system will instantly age your photo using advanced AI old and photo ager technology. ',
      howTo3Title: '3. Download Your Future Face',
      howTo3Description:
        'Preview the result and download your photo aged image in high-quality JPG or PNG format. Try aging your face online free anytime!',
      keyFeature1Title: 'Realistic Face Ager',
      keyFeature1Description:
        'Our age filter and aging filter create real-looking changes — wrinkles, gray hair, and face shape all look natural. Each photo aged result shows what you might truly look like in the future.',
      keyFeature2Title: 'No Watermark',
      keyFeature2Description:
        "You get clean, high-quality images. We never add a watermark to your aged photo — it's yours to keep and share.",
      keyFeature3Title: 'Online & Free to Use',
      keyFeature3Description:
        'No need to download anything. Our AI age progression free online tool runs right in your browser. Try the old age filter, aging filter, or old man filter anytime — 100% free.',
      keyFeature4Title: 'Easy and Fast',
      keyFeature4Description:
        "Just upload a photo, click a button, and see the magic. Anyone can use our age progression tool — no tech skills needed. Whether you're using the face aging online free option or trying the ai aging mode, it's all simple.",
      targetAudience1Title: 'See Yourself Age with Mom',
      targetAudience1Description:
        "Use the age filter on you and your mom's photos. Watch your age progression together and smile at the future.",
      targetAudience2Title: "Age a Child's Photo",
      targetAudience2Description:
        'Curious about what your child might look like as they grow? Try our AI age progression tool to age a photo of your kid in just seconds.',
      targetAudience3Title: 'Grow Old with Your Partner',
      targetAudience3Description:
        "Use our aging filter on couple photos. See what you and your loved one might look like growing old together. It's fun and touching.",
      testimonial1Name: 'Lisa Chen',
      testimonial1Role: 'College Student',
      testimonial1Text:
        'I tried the age filter with my best friend, and we were shocked at how real the results looked. The wrinkles, the hair — everything! The photo aged version was fun to share on social media.',
      testimonial2Name: 'David Miller',
      testimonial2Role: 'Tech Blogger',
      testimonial2Text:
        "As someone who tests new tools all the time, this AI age progression free online app really surprised me. It's super fast, very accurate, and there's no watermark. Definitely worth trying!",
      testimonial3Name: 'Rosa Martinez',
      testimonial3Role: 'Retired Teacher',
      testimonial3Text:
        'My granddaughter showed me how to use this aging filter. We both uploaded pictures and saw our future faces. It was sweet, a little emotional, and very fun.',
      testimonial4Name: 'Emily Zhao',
      testimonial4Role: 'High School Student',
      testimonial4Text:
        "I used the old age filter to make a cool profile picture for my game. Now all my friends are using the face ager too. It's free and works great!",
      testimonial5Name: 'Jake Robertson',
      testimonial5Role: 'Marketing Manager',
      testimonial5Text:
        'I used the AI age progression tool for a social media trend. The old man filter made me laugh, but it also looked so realistic. Great way to go viral!',
      testimonial6Name: 'Sophie & Alex',
      testimonial6Role: 'Newlyweds',
      testimonial6Text:
        "We used the old person filter on our wedding photos to see what we'll look like in 40 years. The photo aged results were sweet and kind of touching. We even framed one!",
      whyUs1Title: 'See Your Older Self in Seconds',
      whyUs1Description:
        "Wondering what you'll look like when you're older? Our AI age progression free online tool lets you age a photo instantly. No app, no sign-up — just upload and see your future face.",
      whyUs1Button: 'Get Your Aged Look',
      whyUs2Title: 'Look Aged, Not Edited',
      whyUs2Description:
        'Forget fake effects. Our age filter and aging filter create smooth, lifelike changes — from fine lines to subtle hair color shifts. The final photo aged version feels real, not overdone.',
      whyUs2Button: 'Age Your Photo Now',
      whyUs3Title: 'Share It, Trend It',
      whyUs3Description:
        'Trying the old person filter has gone viral! Join the trend and post your transformation. Our AI age progression tool is perfect for fun social content and group challenges.',
      whyUs3Button: 'Age Yourself Now',
      whyUs4Title: 'Change Your Look, Stay Private',
      whyUs4Description:
        'Want to stay low-key online? Use our old filter or old age filter to create a new identity. Great for avatars, private chats, or just having fun with your look.',
      whyUs4Button: 'Age Your Face Now',
    },
  },
  de: {
    home: {
      // page.tsx metadata
      title:
        'Der ultimative AI Wasserzeichen-Entferner & AI Hintergrund-Entferner | removeAI',
      description:
        'Verwenden Sie unseren leistungsstarken AI Wasserzeichen-Entferner und AI Hintergrund-Entferner, um Bilder sofort zu bereinigen oder einen professionellen Hintergrund-Unschärfe hinzuzufügen. Das beste kostenlose Wasserzeichen-Entferner-Tool für Ihre Fotos.',
      keywords:
        'AI Wasserzeichen-Entferner, AI Hintergrund-Entferner, Hintergrund-Unschärfe, Wasserzeichen vom Foto entfernen, kostenloses Wasserzeichen-Entferner-Tool, TikTok Wasserzeichen-Entferner, Personen aus Fotos entfernen, Bild bereinigen',

      // page.tsx demo section
      demoSectionTitle: 'Sehen Sie unsere AI in Aktion',
      demoSectionDescription:
        'Beobachten Sie, wie unsere fortschrittliche AI-Technologie Ihre Bilder mit Präzision und Geschwindigkeit transformiert',

      // HeroSection
      heroTitle: 'Kostenlos & Mühelos',
      heroTitleBlue: 'AI Wasserzeichen-Entferner',
      heroTitlePurple: 'AI Hintergrund-Entferner',
      heroDescription:
        'Erleben Sie den besten verfügbaren AI Wasserzeichen-Entferner. Entfernen Sie schnell Wasserzeichen von Fotos mit unserem einfachen, effizienten und sicheren Tool, das Ihre Privatsphäre stets schützt.',
      uploadImage: 'Bild hochladen',
      processing: 'Verarbeitung...',
      freeSecure: 'Kostenlos & Sicher',
      noRegistration: 'Keine Registrierung',
      instantResults: 'Sofortige Ergebnisse',
      dragDropImage: 'Ziehen Sie Ihr Bild hierher',
      supportFormats: 'Unterstützt JPG, PNG, WEBP Formate',
      maxFileSize: 'Maximale Dateigröße: 10MB',
      aiPoweredProcessing: 'AI-gestützte Verarbeitung',
      noImageTryExamples: 'Kein Bild? Probieren Sie diese Beispiele:',
      privacyProtected: '🔒 Ihre Privatsphäre ist geschützt',
      imagesDeleted:
        'Bilder werden sicher verarbeitet und automatisch nach 24 Stunden gelöscht.',
      dropImageHere: 'Bild hier ablegen',
      releaseToUpload: 'Loslassen zum Hochladen und Bearbeitung starten',
      exampleAlt: 'Beispiel',

      // ProductAdvantages
      productAdvantagesTitle:
        'Warum unser AI Hintergrund-Entferner hervorsticht',
      productAdvantagesDescription:
        'Entdecken Sie die Kraft unseres AI Hintergrund-Entferners. Wir bieten Stapel-Hintergrund-Entfernung, die Ihnen Stunden manueller Arbeit erspart. Effizienz und Einfachheit sind in unseren Kern eingebaut.',
      advantage1Title:
        'Hocheffizienter AI Wasserzeichen-Entferner mit kostenlosem, privatem Speicher',
      advantage1Description:
        'Im Gegensatz zu Konkurrenten, die Sie einschränken, bieten wir kostenlosen, sicheren Cloud-Speicher für Ihre bearbeiteten Bilder. Unser System ist darauf ausgelegt, Text aus Bilddateien mit unvergleichlicher Geschwindigkeit zu entfernen, und alle Uploads sind durch unsere strenge Datenschutzrichtlinie geschützt.',
      advantage1Feature1: 'Kostenloser sicherer Cloud-Speicher',
      advantage1Feature2: 'Unvergleichliche Verarbeitungsgeschwindigkeit',
      advantage1Feature3: 'Strenger Datenschutz',
      advantage1Hover: 'Blitzschnelle AI-Verarbeitung',
      advantage2Title:
        'Mehr als ein Filter: Erweiterte Hintergrund-Unschärfe und Objektentfernung',
      advantage2Description:
        'Unser AI Hintergrund-Entferner geht über einfache Bearbeitungen hinaus. Fügen Sie eine professionelle Hintergrund-Unschärfe zu Porträts hinzu oder verwenden Sie unsere fortschrittliche AI, um Bilddateien nahtlos zu bereinigen, indem Sie unerwünschte Objekte entfernen. Es ist ein umfassendes Tool für perfekte Ergebnisse.',
      advantage2Feature1: 'Professionelle Hintergrund-Unschärfe-Effekte',
      advantage2Feature2: 'Erweiterte Objektentfernung',
      advantage2Feature3: 'Umfassende Bearbeitungssuite',
      advantage2Hover: 'AI-gestützte umfassende Bearbeitung',
      tryForFreeNow: 'Jetzt kostenlos testen',
      noRegistrationRequired:
        'Keine Registrierung erforderlich • Unbegrenzte Bildverarbeitung',
    },
    oldfilter: {
      // German translations
    },
  },
  es: {
    home: {
      // page.tsx metadata
      title:
        'El Mejor Removedor de Marcas de Agua AI y Removedor de Fondo AI | removeAI',
      description:
        'Usa nuestro poderoso removedor de marcas de agua AI y removedor de fondo AI para limpiar imágenes instantáneamente o agregar un desenfoque de fondo profesional. La mejor herramienta gratuita para remover marcas de agua de tus fotos.',
      keywords:
        'removedor de marcas de agua AI, removedor de fondo AI, desenfoque de fondo, remover marca de agua de foto, removedor de marcas de agua gratis, removedor de marcas de agua TikTok, remover personas de fotos, limpiar imagen',

      // page.tsx demo section
      demoSectionTitle: 'Ve Nuestra AI en Acción',
      demoSectionDescription:
        'Observa cómo nuestra tecnología AI avanzada transforma tus imágenes con precisión y velocidad',

      // HeroSection
      heroTitle: 'Gratis y Sin Esfuerzo',
      heroTitleBlue: 'Removedor de Marcas de Agua AI',
      heroTitlePurple: 'Removedor de Fondo AI',
      heroDescription:
        'Experimenta el mejor removedor de marcas de agua AI disponible. Remueve rápidamente marcas de agua de fotos con nuestra herramienta simple, eficiente y segura, asegurando que tu privacidad esté siempre protegida.',
      uploadImage: 'Subir Imagen',
      processing: 'Procesando...',
      freeSecure: 'Gratis y Seguro',
      noRegistration: 'Sin Registro',
      instantResults: 'Resultados Instantáneos',
    },
    oldfilter: {
      // Spanish translations
    },
  },
  fr: {
    home: {
      // page.tsx metadata
      title:
        "Le Meilleur Suppresseur de Filigrane IA et Suppresseur d'Arrière-plan IA | removeAI",
      description:
        "Utilisez notre puissant suppresseur de filigrane IA et suppresseur d'arrière-plan IA pour nettoyer instantanément les images ou ajouter un flou d'arrière-plan professionnel. Le meilleur outil gratuit de suppression de filigrane pour vos photos.",
      keywords:
        "suppresseur de filigrane IA, suppresseur d'arrière-plan IA, flou d'arrière-plan, supprimer filigrane de photo, suppresseur de filigrane gratuit, suppresseur de filigrane TikTok, supprimer personnes des photos, nettoyer image",

      // page.tsx demo section
      demoSectionTitle: 'Voyez Notre IA en Action',
      demoSectionDescription:
        'Regardez comment notre technologie IA avancée transforme vos images avec précision et rapidité',

      // HeroSection
      heroTitle: 'Gratuit et Sans Effort',
      heroTitleBlue: 'Suppresseur de Filigrane IA',
      heroTitlePurple: "Suppresseur d'Arrière-plan IA",
      heroDescription:
        'Découvrez le meilleur suppresseur de filigrane IA disponible. Supprimez rapidement les filigranes des photos avec notre outil simple, efficace et sécurisé, garantissant que votre vie privée soit toujours protégée.',
      uploadImage: 'Télécharger Image',
      processing: 'Traitement...',
      freeSecure: 'Gratuit et Sécurisé',
      noRegistration: 'Aucune Inscription',
      instantResults: 'Résultats Instantanés',
    },
    oldfilter: {
      // French translations
    },
  },
  ja: {
    oldfilter: {
      // Japanese translations
    },
  },
  ko: {
    oldfilter: {
      // Korean translations
    },
  },
  pt: {
    oldfilter: {
      // Portuguese translations
    },
  },
  ru: {
    oldfilter: {
      // Russian translations
    },
  },
  th: {
    oldfilter: {
      // Thai translations
    },
  },
  vi: {
    oldfilter: {
      // Vietnamese translations
    },
  },
  'zh-CN': {
    home: {
      // page.tsx metadata
      title: '终极AI水印去除器和AI背景去除器 | removeAI',
      description:
        '使用我们强大的AI水印去除器和AI背景去除器，瞬间清理图像或添加专业背景模糊效果。最好的免费水印去除工具。',
      keywords:
        'AI水印去除器, AI背景去除器, 背景模糊, 从照片中去除水印, 免费水印去除器, TikTok水印去除器, 从照片中去除人物, 清理图像',

      // page.tsx demo section
      demoSectionTitle: '看我们的AI实际效果',
      demoSectionDescription: '观看我们先进的AI技术如何精确快速地转换您的图像',

      // HeroSection
      heroTitle: '免费且轻松',
      heroTitleBlue: 'AI水印去除器',
      heroTitlePurple: 'AI背景去除器',
      heroDescription:
        '体验最好的AI水印去除器。使用我们简单、高效、安全的工具快速从照片中去除水印，确保您的隐私始终受到保护。',
      uploadImage: '上传图片',
      processing: '处理中...',
      freeSecure: '免费且安全',
      noRegistration: '无需注册',
      instantResults: '即时结果',
    },
    oldfilter: {
      // Simplified Chinese translations
    },
  },
  'zh-HK': {
    oldfilter: {
      // Hong Kong Chinese translations
    },
  },
  'zh-TW': {
    oldfilter: {
      // Taiwan Chinese translations
    },
  },
}

const LOCALES_DIR = path.join(__dirname, './packages/i18n/translations')

function addTranslation() {
  fs.readdirSync(LOCALES_DIR).forEach((file) => {
    if (file.endsWith('.json')) {
      const lang = file.replace('.json', '')
      const filePath = path.join(LOCALES_DIR, file)

      try {
        const content = fs.readFileSync(filePath, 'utf8')
        const existingTranslations = JSON.parse(content)
        const newTranslations = translations[lang] || {}

        // Deep merge objects, new values will overwrite old values
        const updatedTranslations = deepMerge(
          existingTranslations,
          newTranslations
        )

        fs.writeFileSync(
          filePath,
          JSON.stringify(updatedTranslations, null, 2) + '\n',
          'utf8'
        )

        console.log(`✅ Successfully updated ${file}`)
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error)
      }
    }
  })
}

// Helper function for deep merging objects
function deepMerge(target, source) {
  const output = { ...target }

  Object.keys(source).forEach((key) => {
    if (isObject(source[key]) && isObject(target[key])) {
      output[key] = deepMerge(target[key], source[key])
    } else {
      output[key] = source[key]
    }
  })

  return output
}

// Helper function to check if an item is an object
function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item)
}

addTranslation()
